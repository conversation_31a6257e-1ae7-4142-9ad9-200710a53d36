2025-06-02T03:15:28.648Z [ERROR] Error getting video info: Error: Command failed with exit code 1: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffprobe-installer\win32-x64\ffprobe.exe -v error -show_format -show_streams C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\perancis-aniversaire-1748834116166-431421.mkv
C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\perancis-aniversaire-1748834116166-431421.mkv: No such file or directory
    at makeError (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\execa\lib\error.js:60:11)
    at handlePromise (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\execa\index.js:118:26)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5) {
  shortMessage: 'Command failed with exit code 1: C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\@ffprobe-installer\\win32-x64\\ffprobe.exe -v error -show_format -show_streams C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\perancis-aniversaire-1748834116166-431421.mkv',
  command: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\@ffprobe-installer\\win32-x64\\ffprobe.exe -v error -show_format -show_streams C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\perancis-aniversaire-1748834116166-431421.mkv',
  escapedCommand: '"C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\@ffprobe-installer\\win32-x64\\ffprobe.exe" -v error -show_format -show_streams "C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\perancis-aniversaire-1748834116166-431421.mkv"',
  exitCode: 1,
  signal: undefined,
  signalDescription: undefined,
  stdout: '',
  stderr: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\perancis-aniversaire-1748834116166-431421.mkv: No such file or directory',
  failed: true,
  timedOut: false,
  isCanceled: false,
  killed: false
}
2025-06-02T03:15:28.649Z [ERROR] Error processing Google Drive import: Error: Command failed with exit code 1: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffprobe-installer\win32-x64\ffprobe.exe -v error -show_format -show_streams C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\perancis-aniversaire-1748834116166-431421.mkv
C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\perancis-aniversaire-1748834116166-431421.mkv: No such file or directory
    at makeError (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\execa\lib\error.js:60:11)
    at handlePromise (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\execa\index.js:118:26)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5) {
  shortMessage: 'Command failed with exit code 1: C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\@ffprobe-installer\\win32-x64\\ffprobe.exe -v error -show_format -show_streams C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\perancis-aniversaire-1748834116166-431421.mkv',
  command: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\@ffprobe-installer\\win32-x64\\ffprobe.exe -v error -show_format -show_streams C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\perancis-aniversaire-1748834116166-431421.mkv',
  escapedCommand: '"C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\@ffprobe-installer\\win32-x64\\ffprobe.exe" -v error -show_format -show_streams "C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\perancis-aniversaire-1748834116166-431421.mkv"',
  exitCode: 1,
  signal: undefined,
  signalDescription: undefined,
  stdout: '',
  stderr: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\perancis-aniversaire-1748834116166-431421.mkv: No such file or directory',
  failed: true,
  timedOut: false,
  isCanceled: false,
  killed: false
}
2025-06-02T03:20:06.125Z [ERROR] Error getting video info: Error: Command failed with exit code 1: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffprobe-installer\win32-x64\ffprobe.exe -v error -show_format -show_streams C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\perancis-aniversaire-1748834397920-436703.mkv
C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\perancis-aniversaire-1748834397920-436703.mkv: No such file or directory
    at makeError (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\execa\lib\error.js:60:11)
    at handlePromise (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\execa\index.js:118:26)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5) {
  shortMessage: 'Command failed with exit code 1: C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\@ffprobe-installer\\win32-x64\\ffprobe.exe -v error -show_format -show_streams C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\perancis-aniversaire-1748834397920-436703.mkv',
  command: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\@ffprobe-installer\\win32-x64\\ffprobe.exe -v error -show_format -show_streams C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\perancis-aniversaire-1748834397920-436703.mkv',
  escapedCommand: '"C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\@ffprobe-installer\\win32-x64\\ffprobe.exe" -v error -show_format -show_streams "C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\perancis-aniversaire-1748834397920-436703.mkv"',
  exitCode: 1,
  signal: undefined,
  signalDescription: undefined,
  stdout: '',
  stderr: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\perancis-aniversaire-1748834397920-436703.mkv: No such file or directory',
  failed: true,
  timedOut: false,
  isCanceled: false,
  killed: false
}
2025-06-02T03:20:06.127Z [ERROR] Error processing Google Drive import: Error: Command failed with exit code 1: C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\@ffprobe-installer\win32-x64\ffprobe.exe -v error -show_format -show_streams C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\perancis-aniversaire-1748834397920-436703.mkv
C:\Users\<USER>\OriDrive\Desktop\streamflow\public\uploads\videos\perancis-aniversaire-1748834397920-436703.mkv: No such file or directory
    at makeError (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\execa\lib\error.js:60:11)
    at handlePromise (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\execa\index.js:118:26)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5) {
  shortMessage: 'Command failed with exit code 1: C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\@ffprobe-installer\\win32-x64\\ffprobe.exe -v error -show_format -show_streams C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\perancis-aniversaire-1748834397920-436703.mkv',
  command: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\@ffprobe-installer\\win32-x64\\ffprobe.exe -v error -show_format -show_streams C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\perancis-aniversaire-1748834397920-436703.mkv',
  escapedCommand: '"C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\node_modules\\@ffprobe-installer\\win32-x64\\ffprobe.exe" -v error -show_format -show_streams "C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\perancis-aniversaire-1748834397920-436703.mkv"',
  exitCode: 1,
  signal: undefined,
  signalDescription: undefined,
  stdout: '',
  stderr: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\perancis-aniversaire-1748834397920-436703.mkv: No such file or directory',
  failed: true,
  timedOut: false,
  isCanceled: false,
  killed: false,
  localFilePath: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\public\\uploads\\videos\\perancis-aniversaire-1748834397920-436703.mkv'
}
2025-06-02T03:36:45.078Z [ERROR] [FFMPEG_STDERR] 25945215-8316-4209-b638-a03c2184d118: Unrecognized option 'stream_loop 0'.
Error splitting the argument list: Option not found
2025-06-02T03:36:45.084Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T03:36:48.151Z [ERROR] [FFMPEG_STDERR] 25945215-8316-4209-b638-a03c2184d118: Unrecognized option 'stream_loop 0'.
Error splitting the argument list:
2025-06-02T03:36:48.156Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T03:36:51.215Z [ERROR] [FFMPEG_STDERR] 25945215-8316-4209-b638-a03c2184d118: Unrecognized option 'stream_loop 0'.
Error splitting the argument list:
2025-06-02T03:36:51.220Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T03:36:54.292Z [ERROR] [FFMPEG_STDERR] 25945215-8316-4209-b638-a03c2184d118: Unrecognized option 'stream_loop 0'.
Error splitting the argument list: Option not found
2025-06-02T03:36:54.297Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T03:36:57.367Z [ERROR] [FFMPEG_STDERR] 25945215-8316-4209-b638-a03c2184d118: Unrecognized option 'stream_loop 0'.
Error splitting the argument list: Option not found
2025-06-02T03:36:57.371Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T03:37:17.490Z [ERROR] [FFMPEG_STDERR] 25945215-8316-4209-b638-a03c2184d118: Unrecognized option 'stream_loop 0'.
Error splitting the argument list: Option not found
2025-06-02T03:37:17.500Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T03:37:20.569Z [ERROR] [FFMPEG_STDERR] 25945215-8316-4209-b638-a03c2184d118: Unrecognized option 'stream_loop 0'.
Error splitting the argument list: Option not found
2025-06-02T03:37:20.573Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T03:37:23.630Z [ERROR] [FFMPEG_STDERR] 25945215-8316-4209-b638-a03c2184d118: Unrecognized option 'stream_loop 0'.
Error splitting the argument list: Option not found
2025-06-02T03:37:23.635Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T03:37:26.702Z [ERROR] [FFMPEG_STDERR] 25945215-8316-4209-b638-a03c2184d118: Unrecognized option 'stream_loop 0'.
Error splitting the argument list: Option not found
2025-06-02T03:37:26.709Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T03:37:29.780Z [ERROR] [FFMPEG_STDERR] 25945215-8316-4209-b638-a03c2184d118: Unrecognized option 'stream_loop 0'.
Error splitting the argument list:
2025-06-02T03:37:29.784Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T03:38:15.738Z [ERROR] [FFMPEG_STDERR] 25945215-8316-4209-b638-a03c2184d118: Unrecognized option 'stream_loop 0'.
Error splitting the argument list:
2025-06-02T03:38:15.742Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T03:38:18.809Z [ERROR] [FFMPEG_STDERR] 25945215-8316-4209-b638-a03c2184d118: Unrecognized option 'stream_loop 0'.
Error splitting the argument list: Option not found
2025-06-02T03:38:18.813Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T03:38:21.905Z [ERROR] [FFMPEG_STDERR] 25945215-8316-4209-b638-a03c2184d118: Unrecognized option 'stream_loop 0'.
Error splitting the argument list: Option not found
2025-06-02T03:38:21.913Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T03:38:24.975Z [ERROR] [FFMPEG_STDERR] 25945215-8316-4209-b638-a03c2184d118: Unrecognized option 'stream_loop 0'.
Error splitting the argument list: Option not found
2025-06-02T03:38:24.979Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T03:38:28.055Z [ERROR] [FFMPEG_STDERR] 25945215-8316-4209-b638-a03c2184d118: Unrecognized option 'stream_loop 0'.
Error splitting the argument list: Option not found
2025-06-02T03:38:28.060Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T04:07:20.768Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T04:07:24.345Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T04:07:27.924Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T04:07:31.498Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T04:07:35.073Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T04:08:05.687Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T04:08:09.275Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T04:08:12.975Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T04:08:16.553Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T04:08:20.442Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25945215-8316-4209-b638-a03c2184d118
2025-06-02T04:34:47.619Z [ERROR] -----------------------------------
2025-06-02T04:34:47.619Z [ERROR] UNCAUGHT EXCEPTION: Error: listen EADDRINUSE: address already in use 0.0.0.0:7575
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at doListen (node:net:2069:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '0.0.0.0',
  port: 7575
}
2025-06-02T04:34:47.621Z [ERROR] -----------------------------------
2025-06-02T04:40:09.898Z [ERROR] Error importing from Google Drive: C:\Users\<USER>\OriDrive\Desktop\streamflow\utils\googleDriveService.js:60
      mimeType: fileMetadata.data.mimeType,
              ^

SyntaxError: Unexpected token ':'
    at internalCompileFunction (node:internal/vm:77:18)
    at wrapSafe (node:internal/modules/cjs/loader:1288:20)
    at Module._compile (node:internal/modules/cjs/loader:1340:27)
    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)
    at Module.load (node:internal/modules/cjs/loader:1207:32)
    at Module._load (node:internal/modules/cjs/loader:1023:12)
    at Module.require (node:internal/modules/cjs/loader:1235:19)
    at require (node:internal/modules/helpers:176:18)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2331:45
2025-06-02T04:40:16.748Z [ERROR] Error importing from Google Drive: C:\Users\<USER>\OriDrive\Desktop\streamflow\utils\googleDriveService.js:60
      mimeType: fileMetadata.data.mimeType,
              ^

SyntaxError: Unexpected token ':'
    at internalCompileFunction (node:internal/vm:77:18)
    at wrapSafe (node:internal/modules/cjs/loader:1288:20)
    at Module._compile (node:internal/modules/cjs/loader:1340:27)
    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)
    at Module.load (node:internal/modules/cjs/loader:1207:32)
    at Module._load (node:internal/modules/cjs/loader:1023:12)
    at Module.require (node:internal/modules/cjs/loader:1235:19)
    at require (node:internal/modules/helpers:176:18)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2331:45
2025-06-02T04:41:41.965Z [ERROR] Error importing from Google Drive: C:\Users\<USER>\OriDrive\Desktop\streamflow\utils\googleDriveService.js:60
      mimeType: fileMetadata.data.mimeType,
              ^

SyntaxError: Unexpected token ':'
    at internalCompileFunction (node:internal/vm:77:18)
    at wrapSafe (node:internal/modules/cjs/loader:1288:20)
    at Module._compile (node:internal/modules/cjs/loader:1340:27)
    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)
    at Module.load (node:internal/modules/cjs/loader:1207:32)
    at Module._load (node:internal/modules/cjs/loader:1023:12)
    at Module.require (node:internal/modules/cjs/loader:1235:19)
    at require (node:internal/modules/helpers:176:18)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2331:45
2025-06-02T04:41:49.325Z [ERROR] Error importing from Google Drive: C:\Users\<USER>\OriDrive\Desktop\streamflow\utils\googleDriveService.js:60
      mimeType: fileMetadata.data.mimeType,
              ^

SyntaxError: Unexpected token ':'
    at internalCompileFunction (node:internal/vm:77:18)
    at wrapSafe (node:internal/modules/cjs/loader:1288:20)
    at Module._compile (node:internal/modules/cjs/loader:1340:27)
    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)
    at Module.load (node:internal/modules/cjs/loader:1207:32)
    at Module._load (node:internal/modules/cjs/loader:1023:12)
    at Module.require (node:internal/modules/cjs/loader:1235:19)
    at require (node:internal/modules/helpers:176:18)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2331:45
2025-06-02T04:42:51.039Z [ERROR] Error importing from Google Drive: C:\Users\<USER>\OriDrive\Desktop\streamflow\utils\googleDriveService.js:60
      mimeType: fileMetadata.data.mimeType,
              ^

SyntaxError: Unexpected token ':'
    at internalCompileFunction (node:internal/vm:77:18)
    at wrapSafe (node:internal/modules/cjs/loader:1288:20)
    at Module._compile (node:internal/modules/cjs/loader:1340:27)
    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)
    at Module.load (node:internal/modules/cjs/loader:1207:32)
    at Module._load (node:internal/modules/cjs/loader:1023:12)
    at Module.require (node:internal/modules/cjs/loader:1235:19)
    at require (node:internal/modules/helpers:176:18)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2331:45
2025-06-02T04:45:10.504Z [ERROR] Error importing from Google Drive: C:\Users\<USER>\OriDrive\Desktop\streamflow\utils\googleDriveService.js:60
      mimeType: fileMetadata.data.mimeType,
              ^

SyntaxError: Unexpected token ':'
    at internalCompileFunction (node:internal/vm:77:18)
    at wrapSafe (node:internal/modules/cjs/loader:1288:20)
    at Module._compile (node:internal/modules/cjs/loader:1340:27)
    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)
    at Module.load (node:internal/modules/cjs/loader:1207:32)
    at Module._load (node:internal/modules/cjs/loader:1023:12)
    at Module.require (node:internal/modules/cjs/loader:1235:19)
    at require (node:internal/modules/helpers:176:18)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2331:45
2025-06-02T04:46:24.707Z [ERROR] Error importing from Google Drive: C:\Users\<USER>\OriDrive\Desktop\streamflow\utils\googleDriveService.js:60
      mimeType: fileMetadata.data.mimeType,
              ^

SyntaxError: Unexpected token ':'
    at internalCompileFunction (node:internal/vm:77:18)
    at wrapSafe (node:internal/modules/cjs/loader:1288:20)
    at Module._compile (node:internal/modules/cjs/loader:1340:27)
    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)
    at Module.load (node:internal/modules/cjs/loader:1207:32)
    at Module._load (node:internal/modules/cjs/loader:1023:12)
    at Module.require (node:internal/modules/cjs/loader:1235:19)
    at require (node:internal/modules/helpers:176:18)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2347:45
2025-06-02T04:46:31.328Z [ERROR] Error importing from Google Drive: C:\Users\<USER>\OriDrive\Desktop\streamflow\utils\googleDriveService.js:60
      mimeType: fileMetadata.data.mimeType,
              ^

SyntaxError: Unexpected token ':'
    at internalCompileFunction (node:internal/vm:77:18)
    at wrapSafe (node:internal/modules/cjs/loader:1288:20)
    at Module._compile (node:internal/modules/cjs/loader:1340:27)
    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)
    at Module.load (node:internal/modules/cjs/loader:1207:32)
    at Module._load (node:internal/modules/cjs/loader:1023:12)
    at Module.require (node:internal/modules/cjs/loader:1235:19)
    at require (node:internal/modules/helpers:176:18)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2347:45
2025-06-02T04:47:01.251Z [ERROR] Error importing from Google Drive: C:\Users\<USER>\OriDrive\Desktop\streamflow\utils\googleDriveService.js:60
      mimeType: fileMetadata.data.mimeType,
              ^

SyntaxError: Unexpected token ':'
    at internalCompileFunction (node:internal/vm:77:18)
    at wrapSafe (node:internal/modules/cjs/loader:1288:20)
    at Module._compile (node:internal/modules/cjs/loader:1340:27)
    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)
    at Module.load (node:internal/modules/cjs/loader:1207:32)
    at Module._load (node:internal/modules/cjs/loader:1023:12)
    at Module.require (node:internal/modules/cjs/loader:1235:19)
    at require (node:internal/modules/helpers:176:18)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2347:45
2025-06-02T04:47:04.764Z [ERROR] Error importing from Google Drive: C:\Users\<USER>\OriDrive\Desktop\streamflow\utils\googleDriveService.js:60
      mimeType: fileMetadata.data.mimeType,
              ^

SyntaxError: Unexpected token ':'
    at internalCompileFunction (node:internal/vm:77:18)
    at wrapSafe (node:internal/modules/cjs/loader:1288:20)
    at Module._compile (node:internal/modules/cjs/loader:1340:27)
    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)
    at Module.load (node:internal/modules/cjs/loader:1207:32)
    at Module._load (node:internal/modules/cjs/loader:1023:12)
    at Module.require (node:internal/modules/cjs/loader:1235:19)
    at require (node:internal/modules/helpers:176:18)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2347:45
2025-06-02T04:49:05.766Z [ERROR] Error importing from Google Drive: C:\Users\<USER>\OriDrive\Desktop\streamflow\utils\googleDriveService.js:60
      mimeType: fileMetadata.data.mimeType,
              ^

SyntaxError: Unexpected token ':'
    at internalCompileFunction (node:internal/vm:77:18)
    at wrapSafe (node:internal/modules/cjs/loader:1288:20)
    at Module._compile (node:internal/modules/cjs/loader:1340:27)
    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)
    at Module.load (node:internal/modules/cjs/loader:1207:32)
    at Module._load (node:internal/modules/cjs/loader:1023:12)
    at Module.require (node:internal/modules/cjs/loader:1235:19)
    at require (node:internal/modules/helpers:176:18)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2347:45
2025-06-02T04:49:05.767Z [ERROR] Error details: {
  message: "Unexpected token ':'",
  stack: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\utils\\googleDriveService.js:60\n' +
    '      mimeType: fileMetadata.data.mimeType,\n' +
    '              ^\n' +
    '\n' +
    "SyntaxError: Unexpected token ':'\n" +
    '    at internalCompileFunction (node:internal/vm:77:18)\n' +
    '    at wrapSafe (node:internal/modules/cjs/loader:1288:20)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1340:27)\n' +
    '    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1207:32)\n' +
    '    at Module._load (node:internal/modules/cjs/loader:1023:12)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1235:19)\n' +
    '    at require (node:internal/modules/helpers:176:18)\n' +
    '    at C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\app.js:2347:45',
  userId: '3e42578a-2600-4584-907f-53af5c4853c6',
  sessionExists: true
}
2025-06-02T04:51:18.279Z [ERROR] Error importing from Google Drive: C:\Users\<USER>\OriDrive\Desktop\streamflow\utils\googleDriveService.js:60
      mimeType: fileMetadata.data.mimeType,
              ^

SyntaxError: Unexpected token ':'
    at internalCompileFunction (node:internal/vm:77:18)
    at wrapSafe (node:internal/modules/cjs/loader:1288:20)
    at Module._compile (node:internal/modules/cjs/loader:1340:27)
    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)
    at Module.load (node:internal/modules/cjs/loader:1207:32)
    at Module._load (node:internal/modules/cjs/loader:1023:12)
    at Module.require (node:internal/modules/cjs/loader:1235:19)
    at require (node:internal/modules/helpers:176:18)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2351:45
2025-06-02T04:51:18.280Z [ERROR] Error details: {
  message: "Unexpected token ':'",
  stack: 'C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\utils\\googleDriveService.js:60\n' +
    '      mimeType: fileMetadata.data.mimeType,\n' +
    '              ^\n' +
    '\n' +
    "SyntaxError: Unexpected token ':'\n" +
    '    at internalCompileFunction (node:internal/vm:77:18)\n' +
    '    at wrapSafe (node:internal/modules/cjs/loader:1288:20)\n' +
    '    at Module._compile (node:internal/modules/cjs/loader:1340:27)\n' +
    '    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n' +
    '    at Module.load (node:internal/modules/cjs/loader:1207:32)\n' +
    '    at Module._load (node:internal/modules/cjs/loader:1023:12)\n' +
    '    at Module.require (node:internal/modules/cjs/loader:1235:19)\n' +
    '    at require (node:internal/modules/helpers:176:18)\n' +
    '    at C:\\Users\\<USER>\\OriDrive\\Desktop\\streamflow\\app.js:2351:45',
  userId: '3e42578a-2600-4584-907f-53af5c4853c6',
  sessionExists: true
}
2025-06-02T04:53:55.822Z [ERROR] Error downloading file from Google Drive: Error: Only MP4 and MOV video files are supported for optimal performance. File type: video/x-matroska, Name: Perancis-Aniversaire.mkv
    at downloadFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\utils\googleDriveService.js:73:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async processGoogleDriveImport (C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2405:20)
2025-06-02T04:53:55.823Z [ERROR] Error processing Google Drive import: Error: Only MP4 and MOV video files are supported for optimal performance. File type: video/x-matroska, Name: Perancis-Aniversaire.mkv
    at downloadFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\utils\googleDriveService.js:73:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async processGoogleDriveImport (C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2405:20)
2025-06-02T04:54:55.351Z [ERROR] Error downloading file from Google Drive: Error: Only MP4 and MOV video files are supported for optimal performance. File type: video/x-matroska, Name: Perancis-Aniversaire.mkv
    at downloadFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\utils\googleDriveService.js:73:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async processGoogleDriveImport (C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2405:20)
2025-06-02T04:54:55.352Z [ERROR] Error processing Google Drive import: Error: Only MP4 and MOV video files are supported for optimal performance. File type: video/x-matroska, Name: Perancis-Aniversaire.mkv
    at downloadFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\utils\googleDriveService.js:73:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async processGoogleDriveImport (C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2405:20)
2025-06-02T05:10:20.471Z [ERROR] Error downloading file from Google Drive: Error: Only MP4 and MOV video files are supported for optimal performance. File type: image/jpeg, Name: 20240425_151212.jpg
    at downloadFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\utils\googleDriveService.js:73:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async processGoogleDriveImport (C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2404:20)
2025-06-02T05:10:20.471Z [ERROR] Error processing Google Drive import: Error: Only MP4 and MOV video files are supported for optimal performance. File type: image/jpeg, Name: 20240425_151212.jpg
    at downloadFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\utils\googleDriveService.js:73:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async processGoogleDriveImport (C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2404:20)
2025-06-02T05:12:07.216Z [ERROR] ⚠️  High memory usage: 94.78%
2025-06-02T05:19:50.923Z [ERROR] Error processing Google Drive import: TypeError: Cannot read properties of undefined (reading 'toFixed')
    at processGoogleDriveImport (C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2429:125)
2025-06-02T05:20:14.065Z [ERROR] Error processing Google Drive import: TypeError: Cannot read properties of undefined (reading 'toFixed')
    at processGoogleDriveImport (C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2429:125)
2025-06-02T05:22:20.079Z [ERROR] Google Drive URL parsing error: Error: Invalid Google Drive URL format
    at extractFileId (C:\Users\<USER>\OriDrive\Desktop\streamflow\utils\googleDriveService.js:28:9)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2352:22
2025-06-02T05:22:28.655Z [ERROR] Error processing Google Drive import: Error: Storage quota exceeded. File size: 0.02GB, Available: -0.00GB (0.02GB used of 0.01GB). Please upgrade your plan or free up space.
    at processGoogleDriveImport (C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2432:15)
2025-06-02T05:23:16.346Z [ERROR] Error processing Google Drive import: Error: Storage quota exceeded. File size: 0.02GB, Available: 0.01GB (0.00GB used of 0.01GB). Please upgrade your plan or free up space.
    at processGoogleDriveImport (C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2432:15)
2025-06-02T05:37:15.301Z [ERROR] Google Drive URL parsing error: Error: Invalid Google Drive URL format
    at extractFileId (C:\Users\<USER>\OriDrive\Desktop\streamflow\utils\googleDriveService.js:28:9)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2372:22
2025-06-02T05:38:37.465Z [ERROR] Error processing Google Drive import: Error: Storage quota exceeded. File size: 0.01GB, Available: 0.01GB (0.01GB used of 0.01GB). Please upgrade your plan or free up space.
    at processGoogleDriveImport (C:\Users\<USER>\OriDrive\Desktop\streamflow\app.js:2452:15)
2025-06-02T07:46:56.847Z [ERROR] Error: Upload ID and chunk index are required
    at DiskStorage.filename [as getFilename] (C:\Users\<USER>\OriDrive\Desktop\streamflow\middleware\uploadMiddleware.js:28:17)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\multer\storage\disk.js:34:10
    at DiskStorage.destination [as getDestination] (C:\Users\<USER>\OriDrive\Desktop\streamflow\middleware\uploadMiddleware.js:22:5)
    at DiskStorage._handleFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\multer\storage\disk.js:31:8)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\multer\lib\make-middleware.js:137:17
    at chunkFilter (C:\Users\<USER>\OriDrive\Desktop\streamflow\middleware\uploadMiddleware.js:48:3)
    at wrappedFileFilter (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\multer\index.js:44:7)
    at Multipart.<anonymous> (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\multer\lib\make-middleware.js:107:7)
    at Multipart.emit (node:events:514:28)
    at HeaderParser.cb (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\busboy\lib\types\multipart.js:358:14)
2025-06-02T07:46:58.101Z [ERROR] Error: Upload ID and chunk index are required
    at DiskStorage.filename [as getFilename] (C:\Users\<USER>\OriDrive\Desktop\streamflow\middleware\uploadMiddleware.js:28:17)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\multer\storage\disk.js:34:10
    at DiskStorage.destination [as getDestination] (C:\Users\<USER>\OriDrive\Desktop\streamflow\middleware\uploadMiddleware.js:22:5)
    at DiskStorage._handleFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\multer\storage\disk.js:31:8)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\multer\lib\make-middleware.js:137:17
    at chunkFilter (C:\Users\<USER>\OriDrive\Desktop\streamflow\middleware\uploadMiddleware.js:48:3)
    at wrappedFileFilter (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\multer\index.js:44:7)
    at Multipart.<anonymous> (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\multer\lib\make-middleware.js:107:7)
    at Multipart.emit (node:events:514:28)
    at HeaderParser.cb (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\busboy\lib\types\multipart.js:358:14)
2025-06-02T07:47:00.300Z [ERROR] Error: Upload ID and chunk index are required
    at DiskStorage.filename [as getFilename] (C:\Users\<USER>\OriDrive\Desktop\streamflow\middleware\uploadMiddleware.js:28:17)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\multer\storage\disk.js:34:10
    at DiskStorage.destination [as getDestination] (C:\Users\<USER>\OriDrive\Desktop\streamflow\middleware\uploadMiddleware.js:22:5)
    at DiskStorage._handleFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\multer\storage\disk.js:31:8)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\multer\lib\make-middleware.js:137:17
    at chunkFilter (C:\Users\<USER>\OriDrive\Desktop\streamflow\middleware\uploadMiddleware.js:48:3)
    at wrappedFileFilter (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\multer\index.js:44:7)
    at Multipart.<anonymous> (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\multer\lib\make-middleware.js:107:7)
    at Multipart.emit (node:events:514:28)
    at HeaderParser.cb (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\busboy\lib\types\multipart.js:358:14)
2025-06-02T07:47:03.485Z [ERROR] Error: Upload ID and chunk index are required
    at DiskStorage.filename [as getFilename] (C:\Users\<USER>\OriDrive\Desktop\streamflow\middleware\uploadMiddleware.js:28:17)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\multer\storage\disk.js:34:10
    at DiskStorage.destination [as getDestination] (C:\Users\<USER>\OriDrive\Desktop\streamflow\middleware\uploadMiddleware.js:22:5)
    at DiskStorage._handleFile (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\multer\storage\disk.js:31:8)
    at C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\multer\lib\make-middleware.js:137:17
    at chunkFilter (C:\Users\<USER>\OriDrive\Desktop\streamflow\middleware\uploadMiddleware.js:48:3)
    at wrappedFileFilter (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\multer\index.js:44:7)
    at Multipart.<anonymous> (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\multer\lib\make-middleware.js:107:7)
    at Multipart.emit (node:events:514:28)
    at HeaderParser.cb (C:\Users\<USER>\OriDrive\Desktop\streamflow\node_modules\busboy\lib\types\multipart.js:358:14)
2025-06-02T23:14:09.921Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 48bd8cb9-acbe-49cc-b881-2bef37509863
2025-06-02T23:14:13.119Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 48bd8cb9-acbe-49cc-b881-2bef37509863
2025-06-02T23:14:16.425Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 48bd8cb9-acbe-49cc-b881-2bef37509863
2025-06-02T23:14:19.595Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 48bd8cb9-acbe-49cc-b881-2bef37509863
2025-06-02T23:14:22.880Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 48bd8cb9-acbe-49cc-b881-2bef37509863
2025-06-03T06:07:29.249Z [ERROR] -----------------------------------
2025-06-03T06:07:29.252Z [ERROR] UNCAUGHT EXCEPTION: Error: listen EADDRINUSE: address already in use 0.0.0.0:7575
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at doListen (node:net:2069:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '0.0.0.0',
  port: 7575
}
2025-06-03T06:07:29.252Z [ERROR] -----------------------------------
2025-06-03T07:28:23.916Z [ERROR] ⚠️  High memory usage: 90.82%
2025-06-03T09:18:29.618Z [ERROR] ⚠️  High memory usage: 93.07%
2025-06-03T09:58:29.958Z [ERROR] ⚠️  High memory usage: 93.90%
2025-06-03T10:08:29.958Z [ERROR] ⚠️  High memory usage: 91.41%
2025-06-03T10:23:29.958Z [ERROR] ⚠️  High memory usage: 91.04%
2025-06-03T11:33:29.967Z [ERROR] ⚠️  High memory usage: 92.80%
2025-06-03T11:38:29.967Z [ERROR] ⚠️  High memory usage: 93.62%
2025-06-03T11:43:29.968Z [ERROR] ⚠️  High memory usage: 94.18%
2025-06-03T12:08:29.971Z [ERROR] ⚠️  High memory usage: 93.78%
2025-06-03T13:37:11.084Z [ERROR] ⚠️  High memory usage: 90.08%
2025-06-03T13:42:11.084Z [ERROR] ⚠️  High memory usage: 93.91%
2025-06-03T14:14:22.900Z [ERROR] ⚠️  High memory usage: 92.58%
2025-06-03T14:49:22.904Z [ERROR] ⚠️  High memory usage: 94.87%
2025-06-03T15:03:55.494Z [ERROR] ⚠️  High memory usage: 91.51%
2025-06-03T15:32:24.542Z [ERROR] ⚠️  High memory usage: 95.26%
2025-06-03T15:37:24.542Z [ERROR] ⚠️  High memory usage: 93.09%
2025-06-03T15:45:28.163Z [ERROR] ⚠️  High memory usage: 91.90%
2025-06-03T16:50:28.169Z [ERROR] ⚠️  High memory usage: 92.38%
2025-06-03T16:55:28.168Z [ERROR] ⚠️  High memory usage: 90.71%
2025-06-03T18:45:28.178Z [ERROR] ⚠️  High memory usage: 90.17%
2025-06-03T18:50:28.179Z [ERROR] ⚠️  High memory usage: 90.98%
2025-06-03T19:55:28.184Z [ERROR] ⚠️  High memory usage: 91.15%
2025-06-03T20:50:28.189Z [ERROR] ⚠️  High memory usage: 92.30%
2025-06-03T21:10:28.190Z [ERROR] ⚠️  High memory usage: 91.71%
2025-06-03T21:45:28.197Z [ERROR] ⚠️  High memory usage: 91.15%
2025-06-03T22:05:28.198Z [ERROR] ⚠️  High memory usage: 91.03%
2025-06-03T22:10:28.197Z [ERROR] ⚠️  High memory usage: 94.00%
2025-06-03T22:20:28.198Z [ERROR] ⚠️  High memory usage: 92.43%
2025-06-03T23:19:26.792Z [ERROR] ⚠️  High memory usage: 90.42%
2025-06-03T23:44:01.388Z [ERROR] ❌ Transaction not found: SOP-A0F2519D-04903F71-1748994237164-EMC01X
2025-06-03T23:45:34.963Z [ERROR] ❌ Transaction not found: SOP-A0F2519D-227F5B5F-1748994332212-YXMAMK
2025-06-03T23:51:15.625Z [ERROR] ❌ Transaction not found: SOP-94A2881C-227F5B5F-1748994672836-I8OJXB
2025-06-03T23:56:56.284Z [ERROR] ❌ Transaction not found: SOP-A0F2519D-04903F71-1748995013574-7NJP0P
2025-06-03T23:58:03.396Z [ERROR] ❌ Transaction not found: SOP-A0F2519D-227F5B5F-1748995077322-RHFX5L
2025-06-04T00:00:01.280Z [ERROR] ❌ Transaction not found: SOP-A0F2519D-04903F71-1748994237164-EMC01X
2025-06-04T00:01:34.954Z [ERROR] ❌ Transaction not found: SOP-A0F2519D-227F5B5F-1748994332212-YXMAMK
2025-06-04T00:03:14.709Z [ERROR] [FFMPEG_STDERR] 4ba7007d-7943-489b-b810-185c13f8838b: av_interleaved_write_frame(): Broken pipe
Error writing trailer of rtmp://a.rtmp.youtube.com/live2/m1xg-9vu4-4j58-ubb5-4mu0: Broken pipe
2025-06-04T00:03:14.724Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 4ba7007d-7943-489b-b810-185c13f8838b
2025-06-04T00:07:15.651Z [ERROR] ❌ Transaction not found: SOP-94A2881C-227F5B5F-1748994672836-I8OJXB
2025-06-04T00:08:56.433Z [ERROR] ❌ Transaction not found: SOP-A0F2519D-04903F71-1748995732681-ROESK6
2025-06-04T00:12:56.718Z [ERROR] ❌ Transaction not found: SOP-A0F2519D-04903F71-1748995013574-7NJP0P
2025-06-04T00:14:04.273Z [ERROR] ❌ Transaction not found: SOP-A0F2519D-227F5B5F-1748995077322-RHFX5L
2025-06-04T00:24:58.106Z [ERROR] ❌ Transaction not found: SOP-A0F2519D-04903F71-1748995732681-ROESK6
2025-06-04T00:42:07.019Z [ERROR] ❌ Transaction not found: SOP-A0F2519D-04903F71-1748997723422-Y2DHZ1
2025-06-04T00:42:34.479Z [ERROR] ❌ Transaction not found: SOP-A0F2519D-227F5B5F-1748997751695-L9NW35
2025-06-04T00:42:49.574Z [ERROR] ❌ Transaction not found: SOP-A0F2519D-BF949D97-1748997763690-C93J0M
2025-06-04T00:43:30.859Z [ERROR] ❌ Transaction not found: SOP-94A2881C-227F5B5F-1748997807930-7TV5ZN
2025-06-04T00:43:42.902Z [ERROR] ❌ Transaction not found: SOP-94A2881C-BF949D97-1748997819372-6PHM3F
2025-06-04T00:46:02.051Z [ERROR] ⚠️  High memory usage: 91.02%
2025-06-04T00:51:02.051Z [ERROR] ⚠️  High memory usage: 95.30%
2025-06-04T00:53:16.167Z [ERROR] ❌ Transaction not found: SOP-A0F2519D-04903F71-1748998391954-WDWP9G
2025-06-04T00:54:01.104Z [ERROR] ❌ Transaction not found: SOP-94A2881C-227F5B5F-1748998438815-YITNQA
2025-06-04T00:55:25.276Z [ERROR] ❌ Transaction not found: SOP-94A2881C-BF949D97-1748998522862-M90S6U
2025-06-04T00:56:49.295Z [ERROR] Error calculating prorated upgrade: Error: Cannot downgrade or select same plan
    at Subscription.calculateProratedUpgrade (/home/<USER>/streamonpod/models/Subscription.js:656:15)
    at async /home/<USER>/streamonpod/routes/payment.js:160:33
2025-06-04T00:56:49.297Z [ERROR] Calculate upgrade error: Error: Cannot downgrade or select same plan
    at Subscription.calculateProratedUpgrade (/home/<USER>/streamonpod/models/Subscription.js:656:15)
    at async /home/<USER>/streamonpod/routes/payment.js:160:33
2025-06-04T00:56:58.341Z [ERROR] Error calculating prorated upgrade: Error: Cannot downgrade or select same plan
    at Subscription.calculateProratedUpgrade (/home/<USER>/streamonpod/models/Subscription.js:656:15)
    at async /home/<USER>/streamonpod/routes/payment.js:160:33
2025-06-04T00:56:58.341Z [ERROR] Calculate upgrade error: Error: Cannot downgrade or select same plan
    at Subscription.calculateProratedUpgrade (/home/<USER>/streamonpod/models/Subscription.js:656:15)
    at async /home/<USER>/streamonpod/routes/payment.js:160:33
2025-06-04T00:57:26.483Z [ERROR] Error calculating prorated upgrade: Error: Cannot downgrade or select same plan
    at Subscription.calculateProratedUpgrade (/home/<USER>/streamonpod/models/Subscription.js:656:15)
    at async /home/<USER>/streamonpod/routes/payment.js:160:33
2025-06-04T00:57:26.483Z [ERROR] Calculate upgrade error: Error: Cannot downgrade or select same plan
    at Subscription.calculateProratedUpgrade (/home/<USER>/streamonpod/models/Subscription.js:656:15)
    at async /home/<USER>/streamonpod/routes/payment.js:160:33
2025-06-04T00:58:07.265Z [ERROR] ❌ Transaction not found: SOP-A0F2519D-04903F71-1748997723422-Y2DHZ1
2025-06-04T00:58:34.673Z [ERROR] ❌ Transaction not found: SOP-A0F2519D-227F5B5F-1748997751695-L9NW35
2025-06-04T00:58:50.438Z [ERROR] ❌ Transaction not found: SOP-A0F2519D-BF949D97-1748997763690-C93J0M
2025-06-04T00:59:31.793Z [ERROR] ❌ Transaction not found: SOP-94A2881C-227F5B5F-1748997807930-7TV5ZN
2025-06-04T00:59:44.015Z [ERROR] ❌ Transaction not found: SOP-94A2881C-BF949D97-1748997819372-6PHM3F
2025-06-04T01:02:06.380Z [ERROR] Error calculating prorated upgrade: Error: Cannot downgrade or select same plan
    at Subscription.calculateProratedUpgrade (/home/<USER>/streamonpod/models/Subscription.js:656:15)
    at async /home/<USER>/streamonpod/routes/payment.js:160:33
2025-06-04T01:02:06.380Z [ERROR] Calculate upgrade error: Error: Cannot downgrade or select same plan
    at Subscription.calculateProratedUpgrade (/home/<USER>/streamonpod/models/Subscription.js:656:15)
    at async /home/<USER>/streamonpod/routes/payment.js:160:33
2025-06-04T01:09:17.129Z [ERROR] ❌ Transaction not found: SOP-A0F2519D-04903F71-1748998391954-WDWP9G
2025-06-04T01:10:02.441Z [ERROR] ❌ Transaction not found: SOP-94A2881C-227F5B5F-1748998438815-YITNQA
2025-06-04T01:11:25.622Z [ERROR] ❌ Transaction not found: SOP-94A2881C-BF949D97-1748998522862-M90S6U
2025-06-04T01:24:16.325Z [ERROR] ⚠️  High memory usage: 92.69%
2025-06-04T01:25:06.629Z [ERROR] Error calculating prorated upgrade: Error: Cannot downgrade or select same plan
    at Subscription.calculateProratedUpgrade (/home/<USER>/streamonpod/models/Subscription.js:790:15)
    at async /home/<USER>/streamonpod/routes/payment.js:160:33
2025-06-04T01:25:06.631Z [ERROR] Calculate upgrade error: Error: Cannot downgrade or select same plan
    at Subscription.calculateProratedUpgrade (/home/<USER>/streamonpod/models/Subscription.js:790:15)
    at async /home/<USER>/streamonpod/routes/payment.js:160:33
2025-06-04T01:25:23.149Z [ERROR] Error calculating prorated upgrade: Error: Cannot downgrade or select same plan
    at Subscription.calculateProratedUpgrade (/home/<USER>/streamonpod/models/Subscription.js:790:15)
    at async /home/<USER>/streamonpod/routes/payment.js:160:33
2025-06-04T01:25:23.149Z [ERROR] Calculate upgrade error: Error: Cannot downgrade or select same plan
    at Subscription.calculateProratedUpgrade (/home/<USER>/streamonpod/models/Subscription.js:790:15)
    at async /home/<USER>/streamonpod/routes/payment.js:160:33
2025-06-04T01:26:49.761Z [ERROR] Error calculating prorated upgrade: Error: Cannot downgrade or select same plan
    at Subscription.calculateProratedUpgrade (/home/<USER>/streamonpod/models/Subscription.js:790:15)
    at async /home/<USER>/streamonpod/routes/payment.js:160:33
2025-06-04T01:26:49.762Z [ERROR] Calculate upgrade error: Error: Cannot downgrade or select same plan
    at Subscription.calculateProratedUpgrade (/home/<USER>/streamonpod/models/Subscription.js:790:15)
    at async /home/<USER>/streamonpod/routes/payment.js:160:33
2025-06-04T01:27:40.147Z [ERROR] ❌ Transaction not found: SOP-94A2881C-BF949D97-1749000456460-P2GZ7D
2025-06-04T01:28:07.445Z [ERROR] Error processing Google Drive import: GaxiosError: API key not valid. Please pass a valid API key.
    at Gaxios._request (/home/<USER>/streamonpod/node_modules/gaxios/build/src/gaxios.js:142:23)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async processGoogleDriveImport (/home/<USER>/streamonpod/app.js:2911:26) {
  config: {
    url: 'https://www.googleapis.com/drive/v3/files/16S4WejqLLogxXf_p6A8NFZsIhhk5Btwr?fields=name%2CmimeType%2Csize&key=092408%3DDian',
    method: 'GET',
    apiVersion: '',
    userAgentDirectives: [ [Object] ],
    paramsSerializer: [Function (anonymous)],
    headers: {
      'x-goog-api-client': 'gdcl/7.2.0 gl-node/20.11.0',
      'Accept-Encoding': 'gzip',
      'User-Agent': 'google-api-nodejs-client/7.2.0 (gzip)'
    },
    params: { fields: 'name,mimeType,size', key: '092408=Dian' },
    validateStatus: [Function (anonymous)],
    retry: true,
    responseType: 'unknown',
    errorRedactor: [Function: defaultErrorRedactor],
    retryConfig: {
      currentRetryAttempt: 0,
      retry: 3,
      httpMethodsToRetry: [Array],
      noResponseRetries: 2,
      retryDelayMultiplier: 2,
      timeOfFirstRequest: 1749000487444,
      totalTimeout: 9007199254740991,
      maxRetryDelay: 9007199254740991,
      statusCodesToRetry: [Array]
    }
  },
  response: {
    config: {
      url: 'https://www.googleapis.com/drive/v3/files/16S4WejqLLogxXf_p6A8NFZsIhhk5Btwr?fields=name%2CmimeType%2Csize&key=092408%3DDian',
      method: 'GET',
      apiVersion: '',
      userAgentDirectives: [Array],
      paramsSerializer: [Function (anonymous)],
      headers: [Object],
      params: [Object],
      validateStatus: [Function (anonymous)],
      retry: true,
      responseType: 'unknown',
      errorRedactor: [Function: defaultErrorRedactor]
    },
    data: { error: [Object] },
    headers: {
      'alt-svc': 'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000',
      'content-encoding': 'gzip',
      'content-type': 'application/json; charset=UTF-8',
      date: 'Wed, 04 Jun 2025 01:28:07 GMT',
      server: 'ESF',
      'transfer-encoding': 'chunked',
      'x-content-type-options': 'nosniff',
      'x-frame-options': 'SAMEORIGIN',
      'x-xss-protection': '0'
    },
    status: 400,
    statusText: 'Bad Request',
    request: {
      responseURL: 'https://www.googleapis.com/drive/v3/files/16S4WejqLLogxXf_p6A8NFZsIhhk5Btwr?fields=name%2CmimeType%2Csize&key=092408%3DDian'
    }
  },
  error: undefined,
  status: 400,
  code: 400,
  errors: [
    {
      message: 'API key not valid. Please pass a valid API key.',
      domain: 'global',
      reason: 'badRequest'
    }
  ],
  [Symbol(gaxios-gaxios-error)]: '6.7.1'
}
2025-06-04T01:38:56.752Z [ERROR] Error calculating prorated upgrade: Error: Cannot downgrade or select same plan
    at Subscription.calculateProratedUpgrade (/home/<USER>/streamonpod/models/Subscription.js:790:15)
    at async /home/<USER>/streamonpod/routes/payment.js:160:33
2025-06-04T01:38:56.755Z [ERROR] Calculate upgrade error: Error: Cannot downgrade or select same plan
    at Subscription.calculateProratedUpgrade (/home/<USER>/streamonpod/models/Subscription.js:790:15)
    at async /home/<USER>/streamonpod/routes/payment.js:160:33
2025-06-04T01:39:01.343Z [ERROR] Error calculating prorated upgrade: Error: Cannot downgrade or select same plan
    at Subscription.calculateProratedUpgrade (/home/<USER>/streamonpod/models/Subscription.js:790:15)
    at async /home/<USER>/streamonpod/routes/payment.js:160:33
2025-06-04T01:39:01.343Z [ERROR] Calculate upgrade error: Error: Cannot downgrade or select same plan
    at Subscription.calculateProratedUpgrade (/home/<USER>/streamonpod/models/Subscription.js:790:15)
    at async /home/<USER>/streamonpod/routes/payment.js:160:33
2025-06-04T01:43:40.316Z [ERROR] ❌ Transaction not found: SOP-94A2881C-BF949D97-1749000456460-P2GZ7D
2025-06-04T01:59:36.199Z [ERROR] ⚠️  High memory usage: 90.02%
2025-06-04T02:04:36.200Z [ERROR] ⚠️  High memory usage: 96.19%
2025-06-04T02:09:36.200Z [ERROR] ⚠️  High memory usage: 96.31%
2025-06-04T02:14:36.201Z [ERROR] ⚠️  High memory usage: 94.86%
2025-06-04T03:12:37.256Z [ERROR] ⚠️  High memory usage: 91.01%
2025-06-04T03:29:17.444Z [ERROR] [FFMPEG_STDERR] 52cbd6f9-ca1a-431a-9638-263377be2dbd: av_interleaved_write_frame(): Connection reset by peer
Error writing trailer of rtmp://a.rtmp.youtube.com/live2/m80c-hk0z-zpj6-8mt5-brjt: Connection reset by peer
2025-06-04T03:29:17.463Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 52cbd6f9-ca1a-431a-9638-263377be2dbd
2025-06-04T03:57:09.858Z [ERROR] [FFMPEG_STDERR] df532246-f11a-4e9b-a3ec-cbcacdb56014: av_interleaved_write_frame(): Broken pipe
Error writing trailer of rtmp://a.rtmp.youtube.com/live2/kjkf-fe7r-rjm8-qpej-91xk: Broken pipe
2025-06-04T03:57:09.870Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream df532246-f11a-4e9b-a3ec-cbcacdb56014
2025-06-04T03:57:11.902Z [ERROR] [FFMPEG_STDERR] 4ba7007d-7943-489b-b810-185c13f8838b: av_interleaved_write_frame(): Broken pipe
Error writing trailer of rtmp://a.rtmp.youtube.com/live2/m1xg-9vu4-4j58-ubb5-4mu0: Broken pipe
2025-06-04T03:57:11.912Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 4ba7007d-7943-489b-b810-185c13f8838b
2025-06-04T04:08:22.134Z [ERROR] [FFMPEG_STDERR] bdbcf5f7-0a45-4c7f-ba37-c569680ecdf3: av_interleaved_write_frame(): Broken pipe
Error writing trailer of rtmp://a.rtmp.youtube.com/live2/gw6c-e7tv-s7a2-mu53-4zyy: Broken pipe
2025-06-04T04:08:22.162Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream bdbcf5f7-0a45-4c7f-ba37-c569680ecdf3
2025-06-04T04:08:22.389Z [ERROR] [FFMPEG_STDERR] df532246-f11a-4e9b-a3ec-cbcacdb56014: av_interleaved_write_frame(): Broken pipe
Error writing trailer of rtmp://a.rtmp.youtube.com/live2/kjkf-fe7r-rjm8-qpej-91xk: Broken pipe
2025-06-04T04:08:22.402Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream df532246-f11a-4e9b-a3ec-cbcacdb56014
2025-06-04T04:08:30.047Z [ERROR] [FFMPEG_STDERR] c537b781-a2a1-4d3a-b209-5f4fc92428cf: av_interleaved_write_frame(): Broken pipe
Error writing trailer of rtmp://a.rtmp.youtube.com/live2/k47s-4ac7-165d-mvg0-8w8h: Broken pipe
2025-06-04T04:08:30.062Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream c537b781-a2a1-4d3a-b209-5f4fc92428cf
2025-06-04T04:32:37.264Z [ERROR] ⚠️  High memory usage: 92.06%
2025-06-04T05:02:37.265Z [ERROR] ⚠️  High memory usage: 90.68%
2025-06-04T05:32:37.267Z [ERROR] ⚠️  High memory usage: 90.38%
2025-06-04T05:47:37.270Z [ERROR] ⚠️  High memory usage: 90.84%
2025-06-04T05:52:37.270Z [ERROR] ⚠️  High memory usage: 90.43%
2025-06-04T07:27:37.275Z [ERROR] ⚠️  High memory usage: 92.04%
2025-06-04T07:57:37.276Z [ERROR] ⚠️  High memory usage: 90.77%
2025-06-04T07:58:55.109Z [ERROR] [FFMPEG_STDERR] 52cbd6f9-ca1a-431a-9638-263377be2dbd: av_interleaved_write_frame(): Broken pipe
Error writing trailer of rtmp://a.rtmp.youtube.com/live2/m80c-hk0z-zpj6-8mt5-brjt: Broken pipe
2025-06-04T07:58:55.127Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 52cbd6f9-ca1a-431a-9638-263377be2dbd
2025-06-04T08:15:46.879Z [ERROR] 🚨 [ERROR] {
  "errorId": "fa8f6f5c-05b2-4a11-a31d-9f5296bb8308",
  "timestamp": "2025-06-04T08:15:46.878Z",
  "type": "STREAMING_ERROR",
  "message": "Stream is already active",
  "statusCode": 500,
  "streamId": "2a8d64bb-62ff-4b3d-8a5c-17132e880441",
  "operation": "startStream",
  "context": {
    "streamId": "2a8d64bb-62ff-4b3d-8a5c-17132e880441",
    "operation": "start"
  }
}
2025-06-04T10:14:56.142Z [ERROR] ⚠️  High memory usage: 91.29%
2025-06-04T10:54:56.146Z [ERROR] ⚠️  High memory usage: 91.23%
2025-06-04T14:28:44.943Z [ERROR] ⚠️  High memory usage: 92.95%
2025-06-04T14:38:44.942Z [ERROR] ⚠️  High memory usage: 90.79%
2025-06-04T14:47:40.638Z [ERROR] [FFMPEG_STDERR] 2c5533fe-2c4d-4897-9dc3-b4c6a237ae2c: av_interleaved_write_frame(): Connection reset by peer
Error writing trailer of rtmp://a.rtmp.youtube.com/live2/14pe-6ecf-41wr-agxj-e0xu: Connection reset by peer
2025-06-04T14:47:40.638Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: av_interleaved_write_frame(): Connection reset by peer
Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Connection reset by peer
2025-06-04T14:47:40.638Z [ERROR] [FFMPEG_STDERR] 26511c7b-4482-4e52-8c53-22d0ab8ac0a1: av_interleaved_write_frame(): Broken pipe
Error writing trailer of rtmp://a.rtmp.youtube.com/live2/0jqm-ys4c-wqbm-t1b0-fsq0: Broken pipe
2025-06-04T14:47:40.644Z [ERROR] [FFMPEG_STDERR] 2a8d64bb-62ff-4b3d-8a5c-17132e880441: av_interleaved_write_frame(): Broken pipe
Error writing trailer of rtmp://a.rtmp.youtube.com/live2/eayf-y83s-s1up-x8br-4w7c: Broken pipe
2025-06-04T14:47:40.645Z [ERROR] [FFMPEG_STDERR] 143764c9-cc86-4a8e-a74c-623d71c79f10: av_interleaved_write_frame(): Broken pipe
Error writing trailer of rtmp://a.rtmp.youtube.com/live2/jx8r-02mb-vf09-uhge-a5xx: Broken pipe
2025-06-04T14:47:40.645Z [ERROR] [FFMPEG_STDERR] ce20e3f1-c9df-4791-a6dc-8cd4c9b0efa8: av_interleaved_write_frame(): Broken pipe
Error writing trailer of rtmp://a.rtmp.youtube.com/live2/paj2-4qu6-gfzt-90tb-38vm: Broken pipe
2025-06-04T14:47:40.658Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 2c5533fe-2c4d-4897-9dc3-b4c6a237ae2c
2025-06-04T14:47:40.660Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-04T14:47:40.660Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 2a8d64bb-62ff-4b3d-8a5c-17132e880441
2025-06-04T14:47:40.660Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 26511c7b-4482-4e52-8c53-22d0ab8ac0a1
2025-06-04T14:47:40.660Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream ce20e3f1-c9df-4791-a6dc-8cd4c9b0efa8
2025-06-04T14:47:40.661Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 143764c9-cc86-4a8e-a74c-623d71c79f10
2025-06-04T14:58:44.944Z [ERROR] ⚠️  High memory usage: 90.38%
2025-06-04T15:33:44.946Z [ERROR] ⚠️  High memory usage: 92.45%
2025-06-04T15:52:00.142Z [ERROR] [FFMPEG_STDERR] c537b781-a2a1-4d3a-b209-5f4fc92428cf: av_interleaved_write_frame(): Broken pipe
Error writing trailer of rtmp://a.rtmp.youtube.com/live2/hu90-sbzt-auq3-b6pb-1s7g: Broken pipe
2025-06-04T15:52:00.163Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream c537b781-a2a1-4d3a-b209-5f4fc92428cf
2025-06-04T15:52:00.219Z [ERROR] [FFMPEG_STDERR] 143764c9-cc86-4a8e-a74c-623d71c79f10: av_interleaved_write_frame(): End of file
Error writing trailer of rtmp://a.rtmp.youtube.com/live2/jx8r-02mb-vf09-uhge-a5xx: End of file
2025-06-04T15:52:00.238Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 143764c9-cc86-4a8e-a74c-623d71c79f10
2025-06-04T15:52:00.989Z [ERROR] [FFMPEG_STDERR] 2c5533fe-2c4d-4897-9dc3-b4c6a237ae2c: av_interleaved_write_frame(): Broken pipe
Error writing trailer of rtmp://a.rtmp.youtube.com/live2/14pe-6ecf-41wr-agxj-e0xu: Broken pipe
2025-06-04T15:52:01.006Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 2c5533fe-2c4d-4897-9dc3-b4c6a237ae2c
2025-06-04T15:52:02.098Z [ERROR] [FFMPEG_STDERR] 52cbd6f9-ca1a-431a-9638-263377be2dbd: av_interleaved_write_frame(): Broken pipe
Error writing trailer of rtmp://a.rtmp.youtube.com/live2/m80c-hk0z-zpj6-8mt5-brjt: Broken pipe
2025-06-04T15:52:02.119Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 52cbd6f9-ca1a-431a-9638-263377be2dbd
2025-06-04T16:08:44.949Z [ERROR] ⚠️  High memory usage: 90.33%
2025-06-04T16:38:44.955Z [ERROR] ⚠️  High memory usage: 90.07%
2025-06-04T16:43:44.955Z [ERROR] ⚠️  High memory usage: 91.60%
2025-06-04T16:53:14.206Z [ERROR] [FFMPEG_STDERR] 7ec6b27e-7297-441c-97d0-5e1dc3515aa9: av_interleaved_write_frame(): Broken pipe
Error writing trailer of rtmp://a.rtmp.youtube.com/live2/sbfj-k540-hfqf-ur81-db7j: Broken pipe
2025-06-04T16:53:14.232Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 7ec6b27e-7297-441c-97d0-5e1dc3515aa9
2025-06-04T17:44:12.893Z [ERROR] [FFMPEG_STDERR] 7ec6b27e-7297-441c-97d0-5e1dc3515aa9: av_interleaved_write_frame(): Connection reset by peer
Error writing trailer of rtmp://a.rtmp.youtube.com/live2/sbfj-k540-hfqf-ur81-db7j: Connection reset by peer
2025-06-04T17:44:12.893Z [ERROR] [FFMPEG_STDERR] 143764c9-cc86-4a8e-a74c-623d71c79f10: av_interleaved_write_frame(): Connection reset by peer
Error writing trailer of rtmp://a.rtmp.youtube.com/live2/jx8r-02mb-vf09-uhge-a5xx: Connection reset by peer
2025-06-04T17:44:12.894Z [ERROR] [FFMPEG_STDERR] c537b781-a2a1-4d3a-b209-5f4fc92428cf: av_interleaved_write_frame(): Connection reset by peer
Error writing trailer of rtmp://a.rtmp.youtube.com/live2/hu90-sbzt-auq3-b6pb-1s7g: Connection reset by peer
2025-06-04T17:44:12.894Z [ERROR] [FFMPEG_STDERR] 26511c7b-4482-4e52-8c53-22d0ab8ac0a1: av_interleaved_write_frame(): Connection reset by peer
Error writing trailer of rtmp://a.rtmp.youtube.com/live2/0jqm-ys4c-wqbm-t1b0-fsq0: Connection reset by peer
2025-06-04T17:44:12.894Z [ERROR] [FFMPEG_STDERR] 86e1821c-d3b8-4119-b84a-4d06ca5fdd86: av_interleaved_write_frame(): Connection reset by peer
Error writing trailer of rtmp://a.rtmp.youtube.com/live2/8w6u-cb1c-huxu-bpk6-4pj3: Connection reset by peer
2025-06-04T17:44:12.894Z [ERROR] [FFMPEG_STDERR] 2c5533fe-2c4d-4897-9dc3-b4c6a237ae2c: av_interleaved_write_frame(): Connection reset by peer
Error writing trailer of rtmp://a.rtmp.youtube.com/live2/14pe-6ecf-41wr-agxj-e0xu: Connection reset by peer
2025-06-04T17:44:12.911Z [ERROR] [FFMPEG_STDERR] 5c945b00-2872-4a2d-9d85-71740097cb66: av_interleaved_write_frame(): Connection reset by peer
Error writing trailer of rtmp://a.rtmp.youtube.com/live2/0zuq-cxu5-0yy0-s2z5-6bdz: Connection reset by peer
2025-06-04T17:44:12.911Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: av_interleaved_write_frame(): Connection reset by peer
Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Connection reset by peer
2025-06-04T17:44:12.911Z [ERROR] [FFMPEG_STDERR] ce20e3f1-c9df-4791-a6dc-8cd4c9b0efa8: av_interleaved_write_frame(): Connection reset by peer
Error writing trailer of rtmp://a.rtmp.youtube.com/live2/paj2-4qu6-gfzt-90tb-38vm: Connection reset by peer
2025-06-04T17:44:12.913Z [ERROR] [FFMPEG_STDERR] c58e0b2a-b090-4071-8e8e-89a8623608dc: av_interleaved_write_frame(): Connection reset by peer
Error writing trailer of rtmp://a.rtmp.youtube.com/live2/aueh-wzh6-6ba7-geu7-3vdg: Connection reset by peer
2025-06-04T17:44:12.914Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 86e1821c-d3b8-4119-b84a-4d06ca5fdd86
2025-06-04T17:44:12.914Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream c537b781-a2a1-4d3a-b209-5f4fc92428cf
2025-06-04T17:44:12.914Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 143764c9-cc86-4a8e-a74c-623d71c79f10
2025-06-04T17:44:12.915Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 2c5533fe-2c4d-4897-9dc3-b4c6a237ae2c
2025-06-04T17:44:12.915Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 7ec6b27e-7297-441c-97d0-5e1dc3515aa9
2025-06-04T17:44:12.915Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-04T17:44:12.916Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 5c945b00-2872-4a2d-9d85-71740097cb66
2025-06-04T17:44:12.920Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream ce20e3f1-c9df-4791-a6dc-8cd4c9b0efa8
2025-06-04T17:44:12.921Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 26511c7b-4482-4e52-8c53-22d0ab8ac0a1
2025-06-04T17:44:12.923Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream c58e0b2a-b090-4071-8e8e-89a8623608dc
2025-06-04T17:44:14.942Z [ERROR] [FFMPEG_STDERR] e9c42792-38f4-43f5-abcb-02a014fb3a76: av_interleaved_write_frame(): Connection reset by peer
Error writing trailer of rtmp://a.rtmp.youtube.com/live2/h1vy-ptek-tekj-aq69-7mcm: Connection reset by peer
2025-06-04T17:44:14.953Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e9c42792-38f4-43f5-abcb-02a014fb3a76
2025-06-04T19:18:44.970Z [ERROR] ⚠️  High memory usage: 91.85%
2025-06-04T19:23:44.970Z [ERROR] ⚠️  High memory usage: 93.77%
2025-06-04T20:08:44.975Z [ERROR] ⚠️  High memory usage: 90.84%
2025-06-04T20:58:44.975Z [ERROR] ⚠️  High memory usage: 91.95%
2025-06-04T21:03:44.977Z [ERROR] ⚠️  High memory usage: 90.63%
2025-06-04T21:08:44.976Z [ERROR] ⚠️  High memory usage: 91.71%
2025-06-04T21:28:44.978Z [ERROR] ⚠️  High memory usage: 92.83%
2025-06-04T21:48:44.985Z [ERROR] ⚠️  High memory usage: 91.12%
2025-06-04T22:33:44.990Z [ERROR] ⚠️  High memory usage: 92.90%
2025-06-04T22:38:44.991Z [ERROR] ⚠️  High memory usage: 94.06%
2025-06-04T22:43:44.992Z [ERROR] ⚠️  High memory usage: 95.00%
2025-06-04T22:48:44.992Z [ERROR] ⚠️  High memory usage: 94.54%
2025-06-04T23:08:44.995Z [ERROR] ⚠️  High memory usage: 90.38%
2025-06-04T23:13:44.994Z [ERROR] ⚠️  High memory usage: 93.06%
2025-06-04T23:48:44.998Z [ERROR] ⚠️  High memory usage: 90.33%
2025-06-05T01:25:46.129Z [ERROR] ❌ Transaction not found: SOP-A0F2519D-04903F71-1749086745053-CGKTMF
2025-06-05T01:41:47.139Z [ERROR] ❌ Transaction not found: SOP-A0F2519D-04903F71-1749086745053-CGKTMF
2025-06-05T01:44:10.536Z [ERROR] ⚠️  High memory usage: 90.11%
2025-06-05T01:59:10.539Z [ERROR] ⚠️  High memory usage: 90.33%
2025-06-05T02:08:56.119Z [ERROR] ❌ Transaction not found: SOP-A0F2519D-04903F71-1749089334600-KECL43
2025-06-05T02:14:10.538Z [ERROR] ⚠️  High memory usage: 93.17%
2025-06-05T02:24:56.170Z [ERROR] ❌ Transaction not found: SOP-A0F2519D-04903F71-1749089334600-KECL43
2025-06-05T03:00:36.826Z [ERROR] ⚠️  High memory usage: 96.69%
2025-06-05T03:15:36.828Z [ERROR] ⚠️  High memory usage: 93.43%
2025-06-05T03:25:36.828Z [ERROR] ⚠️  High memory usage: 92.77%
2025-06-05T03:40:55.216Z [ERROR] ❌ Transaction not found: SOP-A0F2519D-227F5B5F-1749094846732-2Z9H3D
2025-06-05T03:41:19.790Z [ERROR] ❌ Transaction not found: SOP-A0F2519D-227F5B5F-1749094879086-PC3THT
2025-06-05T03:41:34.697Z [ERROR] ❌ Transaction not found: SOP-A0F2519D-04903F71-1749094893407-Z5AH9D
2025-06-05T03:47:30.435Z [ERROR] ⚠️  High memory usage: 92.36%
2025-06-05T03:56:55.854Z [ERROR] ❌ Transaction not found: SOP-A0F2519D-227F5B5F-1749094846732-2Z9H3D
2025-06-05T03:57:22.077Z [ERROR] ❌ Transaction not found: SOP-A0F2519D-227F5B5F-1749094879086-PC3THT
2025-06-05T03:57:35.849Z [ERROR] ❌ Transaction not found: SOP-A0F2519D-04903F71-1749094893407-Z5AH9D
2025-06-05T04:37:30.439Z [ERROR] ⚠️  High memory usage: 91.81%
2025-06-05T04:47:30.441Z [ERROR] ⚠️  High memory usage: 92.65%
2025-06-05T05:02:30.445Z [ERROR] ⚠️  High memory usage: 91.95%
2025-06-05T05:18:13.211Z [ERROR] [FFMPEG_STDERR] c537b781-a2a1-4d3a-b209-5f4fc92428cf: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/hu90-sbzt-auq3-b6pb-1s7g: Broken pipe
2025-06-05T05:18:13.233Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream c537b781-a2a1-4d3a-b209-5f4fc92428cf
2025-06-05T05:59:16.029Z [ERROR] ❌ Transaction not found: SOP-A8E41EF0-04903F71-1749103154514-LJI8DT
2025-06-05T05:59:41.521Z [ERROR] ❌ Transaction not found: SOP-A8E41EF0-04903F71-1749103154514-LJI8DT
2025-06-05T06:02:44.919Z [ERROR] ❌ Transaction not found: SOP-A8E41EF0-04903F71-1749103363683-UWXI4R
2025-06-05T06:03:00.302Z [ERROR] ❌ Transaction not found: SOP-A8E41EF0-04903F71-1749103363683-UWXI4R
2025-06-05T07:37:30.454Z [ERROR] ⚠️  High memory usage: 93.76%
2025-06-05T07:47:30.454Z [ERROR] ⚠️  High memory usage: 91.45%
2025-06-05T07:52:30.456Z [ERROR] ⚠️  High memory usage: 91.53%
2025-06-05T08:11:37.296Z [ERROR] Error adding referred_by column: Error: SQLITE_ERROR: duplicate column name: referred_by
--> in Statement#run([Function: replacement])
    at Database.<anonymous> (/home/<USER>/streamonpod/node_modules/sqlite3/lib/sqlite3.js:76:19)
    at Database.<anonymous> (/home/<USER>/streamonpod/node_modules/sqlite3/lib/sqlite3.js:20:19)
    at Statement.<anonymous> (/home/<USER>/streamonpod/db/database.js:452:10) {
  errno: 1,
  code: 'SQLITE_ERROR',
  __augmented: true
}
2025-06-05T08:16:35.549Z [ERROR] ⚠️  High memory usage: 93.51%
2025-06-05T08:31:35.550Z [ERROR] ⚠️  High memory usage: 91.36%
2025-06-05T09:41:35.560Z [ERROR] ⚠️  High memory usage: 91.20%
2025-06-05T13:31:35.582Z [ERROR] ⚠️  High memory usage: 92.18%
2025-06-05T13:56:35.581Z [ERROR] ⚠️  High memory usage: 91.81%
2025-06-05T14:28:28.886Z [ERROR] [FFMPEG_STDERR] 4c36414d-960e-4910-8c37-c8f0e3d5a486: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/9m69-g5x7-q9ek-pue4-8x6q: Broken pipe
2025-06-05T14:28:28.914Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 4c36414d-960e-4910-8c37-c8f0e3d5a486
2025-06-05T14:51:35.586Z [ERROR] ⚠️  High memory usage: 91.27%
2025-06-05T15:11:35.589Z [ERROR] ⚠️  High memory usage: 91.42%
2025-06-05T15:26:35.590Z [ERROR] ⚠️  High memory usage: 91.82%
2025-06-05T15:58:22.261Z [ERROR] [FFMPEG_STDERR] 88b64c2a-81a6-4f36-959e-c13095298d60: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/2dsq-z2qf-9s9e-wus4-30ex: Connection reset by peer
2025-06-05T15:58:22.275Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 88b64c2a-81a6-4f36-959e-c13095298d60
2025-06-05T16:01:35.594Z [ERROR] ⚠️  High memory usage: 90.92%
2025-06-05T16:26:35.595Z [ERROR] ⚠️  High memory usage: 90.25%
2025-06-06T03:06:35.650Z [ERROR] ⚠️  High memory usage: 93.01%
2025-06-06T03:11:35.649Z [ERROR] ⚠️  High memory usage: 92.49%
2025-06-06T03:31:35.652Z [ERROR] ⚠️  High memory usage: 90.43%
2025-06-06T03:41:35.653Z [ERROR] ⚠️  High memory usage: 92.70%
2025-06-06T04:06:35.655Z [ERROR] ⚠️  High memory usage: 92.38%
2025-06-06T06:11:35.667Z [ERROR] ⚠️  High memory usage: 91.78%
2025-06-06T06:51:35.673Z [ERROR] ⚠️  High memory usage: 90.90%
2025-06-06T07:46:35.676Z [ERROR] ⚠️  High memory usage: 91.43%
2025-06-06T07:51:35.676Z [ERROR] ⚠️  High memory usage: 91.99%
2025-06-06T08:01:35.677Z [ERROR] ⚠️  High memory usage: 90.34%
2025-06-06T08:10:08.612Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-06T08:10:08.630Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-06T09:07:47.169Z [ERROR] [FFMPEG_STDERR] 16da81e2-01e0-4958-8b5a-41f57ea5dbb8: rtmp://a.rtmp.youtube.com/live2/adadad-gesgse-gsegse-gseg: Input/output error
2025-06-06T09:07:47.177Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 16da81e2-01e0-4958-8b5a-41f57ea5dbb8
2025-06-06T09:07:50.486Z [ERROR] [FFMPEG_STDERR] 16da81e2-01e0-4958-8b5a-41f57ea5dbb8: rtmp://a.rtmp.youtube.com/live2/adadad-gesgse-gsegse-gseg: Input/output error
2025-06-06T09:07:50.499Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 16da81e2-01e0-4958-8b5a-41f57ea5dbb8
2025-06-06T09:07:53.819Z [ERROR] [FFMPEG_STDERR] 16da81e2-01e0-4958-8b5a-41f57ea5dbb8: rtmp://a.rtmp.youtube.com/live2/adadad-gesgse-gsegse-gseg: Input/output error
2025-06-06T09:07:53.827Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 16da81e2-01e0-4958-8b5a-41f57ea5dbb8
2025-06-06T09:07:57.122Z [ERROR] [FFMPEG_STDERR] 16da81e2-01e0-4958-8b5a-41f57ea5dbb8: rtmp://a.rtmp.youtube.com/live2/adadad-gesgse-gsegse-gseg: Input/output error
2025-06-06T09:07:57.129Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 16da81e2-01e0-4958-8b5a-41f57ea5dbb8
2025-06-06T09:08:00.418Z [ERROR] [FFMPEG_STDERR] 16da81e2-01e0-4958-8b5a-41f57ea5dbb8: rtmp://a.rtmp.youtube.com/live2/adadad-gesgse-gsegse-gseg: Input/output error
2025-06-06T09:08:00.423Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 16da81e2-01e0-4958-8b5a-41f57ea5dbb8
2025-06-06T09:17:12.141Z [ERROR] ⚠️  High memory usage: 94.67%
2025-06-06T09:22:12.143Z [ERROR] ⚠️  High memory usage: 91.42%
2025-06-06T09:42:12.144Z [ERROR] ⚠️  High memory usage: 90.03%
2025-06-06T09:47:12.145Z [ERROR] ⚠️  High memory usage: 93.24%
2025-06-06T09:57:12.146Z [ERROR] ⚠️  High memory usage: 92.64%
2025-06-06T10:07:12.146Z [ERROR] ⚠️  High memory usage: 91.96%
2025-06-06T10:38:10.246Z [ERROR] ❌ Failed to process chunk 1: Error: Chunk 1 already received
    at ChunkedUploadService.processChunk (/home/<USER>/streamonpod/utils/chunkedUploadService.js:114:15)
    at async /home/<USER>/streamonpod/app.js:2368:20
2025-06-06T10:38:10.248Z [ERROR] ❌ Error processing chunk: Error: Failed to process chunk: Chunk 1 already received
    at ChunkedUploadService.processChunk (/home/<USER>/streamonpod/utils/chunkedUploadService.js:155:13)
    at async /home/<USER>/streamonpod/app.js:2368:20
2025-06-06T10:41:48.318Z [ERROR] ❌ Failed to process chunk 1: Error: Chunk 1 already received
    at ChunkedUploadService.processChunk (/home/<USER>/streamonpod/utils/chunkedUploadService.js:114:15)
    at async /home/<USER>/streamonpod/app.js:2368:20
2025-06-06T10:41:48.319Z [ERROR] ❌ Error processing chunk: Error: Failed to process chunk: Chunk 1 already received
    at ChunkedUploadService.processChunk (/home/<USER>/streamonpod/utils/chunkedUploadService.js:155:13)
    at async /home/<USER>/streamonpod/app.js:2368:20
2025-06-06T10:42:12.146Z [ERROR] ⚠️  High memory usage: 90.33%
2025-06-06T10:44:37.460Z [ERROR] ❌ Failed to process chunk 1: Error: Chunk 1 already received
    at ChunkedUploadService.processChunk (/home/<USER>/streamonpod/utils/chunkedUploadService.js:114:15)
    at async /home/<USER>/streamonpod/app.js:2368:20
2025-06-06T10:44:37.461Z [ERROR] ❌ Error processing chunk: Error: Failed to process chunk: Chunk 1 already received
    at ChunkedUploadService.processChunk (/home/<USER>/streamonpod/utils/chunkedUploadService.js:155:13)
    at async /home/<USER>/streamonpod/app.js:2368:20
2025-06-06T10:47:12.146Z [ERROR] ⚠️  High memory usage: 91.31%
2025-06-06T11:04:49.758Z [ERROR] ❌ Dragonfly main client error: write ECONNRESET
2025-06-06T11:04:49.761Z [ERROR] ❌ Dragonfly main client error: write EPIPE
2025-06-06T11:04:49.866Z [ERROR] ❌ Dragonfly main client error: write EPIPE
2025-06-06T11:04:49.868Z [ERROR] ❌ Dragonfly main client error: write EPIPE
2025-06-06T11:04:50.019Z [ERROR] ❌ Dragonfly main client error: connect ECONNREFUSED 127.0.0.1:6379
2025-06-06T11:04:50.020Z [ERROR] ❌ Dragonfly main client error: connect ECONNREFUSED 127.0.0.1:6379
2025-06-06T11:04:50.237Z [ERROR] ❌ Dragonfly main client error: connect ECONNREFUSED 127.0.0.1:6379
2025-06-06T11:04:50.238Z [ERROR] ❌ Dragonfly main client error: connect ECONNREFUSED 127.0.0.1:6379
2025-06-06T11:15:10.306Z [ERROR] ❌ Upload session not found: 2021aef2-863c-4be6-b5a8-f1959e9a472f
2025-06-06T11:15:10.329Z [ERROR] ❌ Failed to process chunk 1: Error: Upload session not found or expired
    at ChunkedUploadService.processChunk (/home/<USER>/streamonpod/utils/chunkedUploadService.js:90:15)
    at async /home/<USER>/streamonpod/app.js:2379:20
2025-06-06T11:15:10.331Z [ERROR] ❌ Error processing chunk: Error: Failed to process chunk: Upload session not found or expired
    at ChunkedUploadService.processChunk (/home/<USER>/streamonpod/utils/chunkedUploadService.js:155:13)
    at async /home/<USER>/streamonpod/app.js:2379:20
2025-06-06T11:45:19.562Z [ERROR] ⚠️  High memory usage: 90.80%
2025-06-06T12:26:24.360Z [ERROR] ⚠️  High memory usage: 92.13%
2025-06-06T12:41:54.155Z [ERROR] ⚠️  High memory usage: 90.59%
2025-06-06T12:46:54.154Z [ERROR] ⚠️  High memory usage: 92.66%
2025-06-06T12:51:54.154Z [ERROR] ⚠️  High memory usage: 90.07%
2025-06-06T13:01:54.155Z [ERROR] ⚠️  High memory usage: 90.98%
2025-06-06T13:19:07.088Z [ERROR] 🚨 [ERROR] {
  "errorId": "a2361828-6eae-44a6-93b1-9dec1d0a7293",
  "timestamp": "2025-06-06T13:19:07.087Z",
  "type": "STREAMING_ERROR",
  "message": "Stream is already active",
  "statusCode": 500,
  "streamId": "c58e0b2a-b090-4071-8e8e-89a8623608dc",
  "operation": "startStream",
  "context": {
    "streamId": "c58e0b2a-b090-4071-8e8e-89a8623608dc",
    "operation": "start"
  }
}
2025-06-06T13:19:07.090Z [ERROR] 🚨 [ERROR] {
  "errorId": "3e6d32af-cc07-48f2-a31e-254af8b7b0ae",
  "timestamp": "2025-06-06T13:19:07.090Z",
  "type": "STREAMING_ERROR",
  "message": "Stream is already active",
  "statusCode": 500,
  "streamId": "e9c42792-38f4-43f5-abcb-02a014fb3a76",
  "operation": "startStream",
  "context": {
    "streamId": "e9c42792-38f4-43f5-abcb-02a014fb3a76",
    "operation": "start"
  }
}
2025-06-06T13:19:07.095Z [ERROR] 🚨 [ERROR] {
  "errorId": "373d4a76-b34f-448e-ac3e-8708975c82d2",
  "timestamp": "2025-06-06T13:19:07.095Z",
  "type": "STREAMING_ERROR",
  "message": "Stream is already active",
  "statusCode": 500,
  "streamId": "86e1821c-d3b8-4119-b84a-4d06ca5fdd86",
  "operation": "startStream",
  "context": {
    "streamId": "86e1821c-d3b8-4119-b84a-4d06ca5fdd86",
    "operation": "start"
  }
}
2025-06-06T13:33:29.068Z [ERROR] ⚠️  High memory usage: 91.81%
2025-06-06T13:38:29.068Z [ERROR] ⚠️  High memory usage: 92.19%
2025-06-06T13:53:29.070Z [ERROR] ⚠️  High memory usage: 90.99%
2025-06-06T14:10:04.339Z [ERROR] [FFMPEG_STDERR] 70282254-3d74-4cba-b4b8-2a3a8780c36b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/jx8r-02mb-vf09-uhge-a5xx: Broken pipe
2025-06-06T14:10:04.360Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 70282254-3d74-4cba-b4b8-2a3a8780c36b
2025-06-06T14:13:29.072Z [ERROR] ⚠️  High memory usage: 90.77%
2025-06-06T14:21:25.206Z [ERROR] [FFMPEG_STDERR] 86e1821c-d3b8-4119-b84a-4d06ca5fdd86: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/8w6u-cb1c-huxu-bpk6-4pj3: Broken pipe
2025-06-06T14:21:25.229Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 86e1821c-d3b8-4119-b84a-4d06ca5fdd86
2025-06-06T14:23:29.071Z [ERROR] ⚠️  High memory usage: 90.96%
2025-06-06T14:33:29.072Z [ERROR] ⚠️  High memory usage: 91.63%
2025-06-06T14:48:29.076Z [ERROR] ⚠️  High memory usage: 90.73%
2025-06-06T15:03:29.077Z [ERROR] ⚠️  High memory usage: 92.31%
2025-06-06T15:13:29.077Z [ERROR] ⚠️  High memory usage: 90.33%
2025-06-06T15:28:29.078Z [ERROR] ⚠️  High memory usage: 91.63%
2025-06-06T15:33:29.078Z [ERROR] ⚠️  High memory usage: 93.07%
2025-06-06T15:38:29.079Z [ERROR] ⚠️  High memory usage: 91.27%
2025-06-06T15:43:29.079Z [ERROR] ⚠️  High memory usage: 94.31%
2025-06-06T15:48:29.079Z [ERROR] ⚠️  High memory usage: 95.25%
2025-06-06T15:53:29.080Z [ERROR] ⚠️  High memory usage: 93.42%
2025-06-06T15:58:29.081Z [ERROR] ⚠️  High memory usage: 91.15%
2025-06-06T16:08:29.083Z [ERROR] ⚠️  High memory usage: 91.87%
2025-06-06T16:13:29.083Z [ERROR] ⚠️  High memory usage: 94.22%
2025-06-06T16:18:29.083Z [ERROR] ⚠️  High memory usage: 92.99%
2025-06-06T16:38:29.086Z [ERROR] ⚠️  High memory usage: 92.53%
2025-06-06T16:43:29.086Z [ERROR] ⚠️  High memory usage: 95.01%
2025-06-06T19:58:29.098Z [ERROR] ⚠️  High memory usage: 90.50%
2025-06-06T21:23:29.107Z [ERROR] ⚠️  High memory usage: 93.30%
2025-06-06T21:33:29.108Z [ERROR] ⚠️  High memory usage: 91.26%
2025-06-06T21:58:29.108Z [ERROR] ⚠️  High memory usage: 92.73%
2025-06-06T22:03:29.109Z [ERROR] ⚠️  High memory usage: 90.54%
2025-06-06T22:08:29.110Z [ERROR] ⚠️  High memory usage: 91.68%
2025-06-06T22:13:29.110Z [ERROR] ⚠️  High memory usage: 91.91%
2025-06-06T22:30:32.240Z [ERROR] ⚠️  High memory usage: 92.43%
2025-06-06T22:35:32.241Z [ERROR] ⚠️  High memory usage: 93.49%
2025-06-06T22:40:32.242Z [ERROR] ⚠️  High memory usage: 92.02%
2025-06-06T22:43:18.623Z [ERROR] ❌ Failed to process chunk 0: Error: Chunk 0 already received
    at ChunkedUploadService.processChunk (/home/<USER>/streamonpod/utils/chunkedUploadService.js:114:15)
    at async /home/<USER>/streamonpod/app.js:2396:20
2025-06-06T22:43:18.624Z [ERROR] ❌ Error processing chunk: Error: Failed to process chunk: Chunk 0 already received
    at ChunkedUploadService.processChunk (/home/<USER>/streamonpod/utils/chunkedUploadService.js:155:13)
    at async /home/<USER>/streamonpod/app.js:2396:20
2025-06-06T22:45:32.242Z [ERROR] ⚠️  High memory usage: 91.29%
2025-06-06T22:46:28.762Z [ERROR] ❌ Failed to process chunk 0: Error: Chunk 0 already received
    at ChunkedUploadService.processChunk (/home/<USER>/streamonpod/utils/chunkedUploadService.js:114:15)
    at async /home/<USER>/streamonpod/app.js:2396:20
2025-06-06T22:46:28.764Z [ERROR] ❌ Error processing chunk: Error: Failed to process chunk: Chunk 0 already received
    at ChunkedUploadService.processChunk (/home/<USER>/streamonpod/utils/chunkedUploadService.js:155:13)
    at async /home/<USER>/streamonpod/app.js:2396:20
2025-06-06T22:55:32.243Z [ERROR] ⚠️  High memory usage: 91.87%
2025-06-06T23:09:17.654Z [ERROR] ⚠️  High memory usage: 90.04%
2025-06-06T23:24:17.656Z [ERROR] ⚠️  High memory usage: 93.83%
2025-06-06T23:29:17.656Z [ERROR] ⚠️  High memory usage: 90.08%
2025-06-06T23:34:17.656Z [ERROR] ⚠️  High memory usage: 90.87%
2025-06-06T23:39:17.656Z [ERROR] ⚠️  High memory usage: 92.07%
2025-06-06T23:42:48.200Z [ERROR] Error initializing chunked upload: Error: Failed to initialize upload: Chunk size too large. Maximum chunk size is 10MB
    at ChunkedUploadService.initializeUpload (/home/<USER>/streamonpod/utils/chunkedUploadService.js:72:13)
    at /home/<USER>/streamonpod/app.js:2320:51
2025-06-06T23:42:55.859Z [ERROR] Error initializing chunked upload: Error: Failed to initialize upload: Chunk size too large. Maximum chunk size is 10MB
    at ChunkedUploadService.initializeUpload (/home/<USER>/streamonpod/utils/chunkedUploadService.js:72:13)
    at /home/<USER>/streamonpod/app.js:2320:51
2025-06-06T23:43:05.448Z [ERROR] Error initializing chunked upload: Error: Failed to initialize upload: Chunk size too large. Maximum chunk size is 10MB
    at ChunkedUploadService.initializeUpload (/home/<USER>/streamonpod/utils/chunkedUploadService.js:72:13)
    at /home/<USER>/streamonpod/app.js:2320:51
2025-06-06T23:43:36.059Z [ERROR] Error initializing chunked upload: Error: Failed to initialize upload: Chunk size too large. Maximum chunk size is 10MB
    at ChunkedUploadService.initializeUpload (/home/<USER>/streamonpod/utils/chunkedUploadService.js:72:13)
    at /home/<USER>/streamonpod/app.js:2320:51
2025-06-06T23:56:06.532Z [ERROR] ⚠️  High memory usage: 90.67%
2025-06-07T00:16:06.534Z [ERROR] ⚠️  High memory usage: 91.83%
2025-06-07T00:21:06.534Z [ERROR] ⚠️  High memory usage: 93.08%
2025-06-07T01:16:06.537Z [ERROR] ⚠️  High memory usage: 92.84%
2025-06-07T01:21:06.538Z [ERROR] ⚠️  High memory usage: 91.54%
2025-06-07T01:21:20.450Z [ERROR] [FFMPEG_STDERR] 51a46058-2ebe-47aa-bc3e-8ec7dceb56a7: rtmp://a.rtmp.youtube.com/live2/dsafsf-djydj65-hynd6tu5-iujdytu: Input/output error
2025-06-07T01:21:20.458Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 51a46058-2ebe-47aa-bc3e-8ec7dceb56a7
2025-06-07T01:21:23.791Z [ERROR] [FFMPEG_STDERR] 51a46058-2ebe-47aa-bc3e-8ec7dceb56a7: rtmp://a.rtmp.youtube.com/live2/dsafsf-djydj65-hynd6tu5-iujdytu: Input/output error
2025-06-07T01:21:23.797Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 51a46058-2ebe-47aa-bc3e-8ec7dceb56a7
2025-06-07T01:21:27.085Z [ERROR] [FFMPEG_STDERR] 51a46058-2ebe-47aa-bc3e-8ec7dceb56a7: rtmp://a.rtmp.youtube.com/live2/dsafsf-djydj65-hynd6tu5-iujdytu: Input/output error
2025-06-07T01:21:27.092Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 51a46058-2ebe-47aa-bc3e-8ec7dceb56a7
2025-06-07T01:21:30.404Z [ERROR] [FFMPEG_STDERR] 51a46058-2ebe-47aa-bc3e-8ec7dceb56a7: rtmp://a.rtmp.youtube.com/live2/dsafsf-djydj65-hynd6tu5-iujdytu: Input/output error
2025-06-07T01:21:30.414Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 51a46058-2ebe-47aa-bc3e-8ec7dceb56a7
2025-06-07T01:21:33.699Z [ERROR] [FFMPEG_STDERR] 51a46058-2ebe-47aa-bc3e-8ec7dceb56a7: rtmp://a.rtmp.youtube.com/live2/dsafsf-djydj65-hynd6tu5-iujdytu: Input/output error
2025-06-07T01:21:33.706Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 51a46058-2ebe-47aa-bc3e-8ec7dceb56a7
2025-06-07T01:22:03.984Z [ERROR] [FFMPEG_STDERR] 51a46058-2ebe-47aa-bc3e-8ec7dceb56a7: rtmp://a.rtmp.youtube.com/live2/dsafsf-djydj65-hynd6tu5-iujdytu: Input/output error
2025-06-07T01:22:04.009Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 51a46058-2ebe-47aa-bc3e-8ec7dceb56a7
2025-06-07T01:22:07.291Z [ERROR] [FFMPEG_STDERR] 51a46058-2ebe-47aa-bc3e-8ec7dceb56a7: rtmp://a.rtmp.youtube.com/live2/dsafsf-djydj65-hynd6tu5-iujdytu: Input/output error
2025-06-07T01:22:07.300Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 51a46058-2ebe-47aa-bc3e-8ec7dceb56a7
2025-06-07T01:22:10.593Z [ERROR] [FFMPEG_STDERR] 51a46058-2ebe-47aa-bc3e-8ec7dceb56a7: rtmp://a.rtmp.youtube.com/live2/dsafsf-djydj65-hynd6tu5-iujdytu: Input/output error
2025-06-07T01:22:10.601Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 51a46058-2ebe-47aa-bc3e-8ec7dceb56a7
2025-06-07T01:22:13.891Z [ERROR] [FFMPEG_STDERR] 51a46058-2ebe-47aa-bc3e-8ec7dceb56a7: rtmp://a.rtmp.youtube.com/live2/dsafsf-djydj65-hynd6tu5-iujdytu: Input/output error
2025-06-07T01:22:13.898Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 51a46058-2ebe-47aa-bc3e-8ec7dceb56a7
2025-06-07T01:22:17.193Z [ERROR] [FFMPEG_STDERR] 51a46058-2ebe-47aa-bc3e-8ec7dceb56a7: rtmp://a.rtmp.youtube.com/live2/dsafsf-djydj65-hynd6tu5-iujdytu: Input/output error
2025-06-07T01:22:17.200Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 51a46058-2ebe-47aa-bc3e-8ec7dceb56a7
2025-06-07T01:26:06.538Z [ERROR] ⚠️  High memory usage: 92.80%
2025-06-07T01:49:48.940Z [ERROR] [FFMPEG_STDERR] 51a46058-2ebe-47aa-bc3e-8ec7dceb56a7: rtmp://a.rtmp.youtube.com/live2/dsafsf-djydj65-hynd6tu5-iujdytu: Input/output error
2025-06-07T01:49:48.947Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 51a46058-2ebe-47aa-bc3e-8ec7dceb56a7
2025-06-07T01:49:52.240Z [ERROR] [FFMPEG_STDERR] 51a46058-2ebe-47aa-bc3e-8ec7dceb56a7: rtmp://a.rtmp.youtube.com/live2/dsafsf-djydj65-hynd6tu5-iujdytu: Input/output error
2025-06-07T01:49:52.249Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 51a46058-2ebe-47aa-bc3e-8ec7dceb56a7
2025-06-07T01:49:55.545Z [ERROR] [FFMPEG_STDERR] 51a46058-2ebe-47aa-bc3e-8ec7dceb56a7: rtmp://a.rtmp.youtube.com/live2/dsafsf-djydj65-hynd6tu5-iujdytu: Input/output error
2025-06-07T01:49:55.552Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 51a46058-2ebe-47aa-bc3e-8ec7dceb56a7
2025-06-07T01:49:58.830Z [ERROR] [FFMPEG_STDERR] 51a46058-2ebe-47aa-bc3e-8ec7dceb56a7: rtmp://a.rtmp.youtube.com/live2/dsafsf-djydj65-hynd6tu5-iujdytu: Input/output error
2025-06-07T01:49:58.837Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 51a46058-2ebe-47aa-bc3e-8ec7dceb56a7
2025-06-07T01:50:02.325Z [ERROR] [FFMPEG_STDERR] 51a46058-2ebe-47aa-bc3e-8ec7dceb56a7: rtmp://a.rtmp.youtube.com/live2/dsafsf-djydj65-hynd6tu5-iujdytu: Input/output error
2025-06-07T01:50:02.344Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 51a46058-2ebe-47aa-bc3e-8ec7dceb56a7
2025-06-07T02:03:07.478Z [ERROR] Google Drive URL parsing error: Error: Invalid Google Drive URL format
    at extractFileId (/home/<USER>/streamonpod/utils/googleDriveService.js:294:9)
    at /home/<USER>/streamonpod/app.js:2982:22
2025-06-07T02:03:32.697Z [ERROR] Google Drive URL parsing error: Error: Invalid Google Drive URL format
    at extractFileId (/home/<USER>/streamonpod/utils/googleDriveService.js:294:9)
    at /home/<USER>/streamonpod/app.js:2982:22
2025-06-07T02:04:29.561Z [ERROR] [FFMPEG_STDERR] 51a46058-2ebe-47aa-bc3e-8ec7dceb56a7: rtmp://a.rtmp.youtube.com/live2/dsafsf-djydj65-hynd6tu5-iujdytu: Input/output error
2025-06-07T02:04:29.569Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 51a46058-2ebe-47aa-bc3e-8ec7dceb56a7
2025-06-07T02:04:33.430Z [ERROR] [FFMPEG_STDERR] 51a46058-2ebe-47aa-bc3e-8ec7dceb56a7: rtmp://a.rtmp.youtube.com/live2/dsafsf-djydj65-hynd6tu5-iujdytu: Input/output error
2025-06-07T02:04:33.436Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 51a46058-2ebe-47aa-bc3e-8ec7dceb56a7
2025-06-07T02:04:37.290Z [ERROR] [FFMPEG_STDERR] 51a46058-2ebe-47aa-bc3e-8ec7dceb56a7: rtmp://a.rtmp.youtube.com/live2/dsafsf-djydj65-hynd6tu5-iujdytu: Input/output error
2025-06-07T02:04:37.297Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 51a46058-2ebe-47aa-bc3e-8ec7dceb56a7
2025-06-07T02:04:41.293Z [ERROR] [FFMPEG_STDERR] 51a46058-2ebe-47aa-bc3e-8ec7dceb56a7: rtmp://a.rtmp.youtube.com/live2/dsafsf-djydj65-hynd6tu5-iujdytu: Input/output error
2025-06-07T02:04:41.300Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 51a46058-2ebe-47aa-bc3e-8ec7dceb56a7
2025-06-07T02:04:45.135Z [ERROR] [FFMPEG_STDERR] 51a46058-2ebe-47aa-bc3e-8ec7dceb56a7: rtmp://a.rtmp.youtube.com/live2/dsafsf-djydj65-hynd6tu5-iujdytu: Input/output error
2025-06-07T02:04:45.142Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 51a46058-2ebe-47aa-bc3e-8ec7dceb56a7
2025-06-07T02:14:12.134Z [ERROR] ❌ Error checking slot limits for user 026eaac8-1fa9-449f-a0fd-0f4fd709fbeb: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:14:12.137Z [ERROR] ❌ Error checking slot limits for user 11941296-378f-4c73-8991-038f0c741401: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:14:12.158Z [ERROR] ❌ Error checking slot limits for user 5556e252-f8df-4df2-af35-9bb444117009: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:14:12.163Z [ERROR] ❌ Error checking slot limits for user 59bd3e3f-74d5-4bb3-99c6-c4bccf67b3c2: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:14:12.170Z [ERROR] ❌ Error checking slot limits for user b852e3c3-50a6-45dc-9a77-3ab64dec6936: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:14:12.172Z [ERROR] ❌ Error checking slot limits for user b88465d0-3e6f-4987-b1cb-dd0e566bfa00: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:14:12.174Z [ERROR] ❌ Error checking slot limits for user e631ebe8-4129-4550-9635-6f02860ac6fe: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:14:12.176Z [ERROR] ❌ Error checking slot limits for user f7a3387e-93d9-42a4-b484-c12776bd1d1d: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:19:10.551Z [ERROR] ⚠️  High memory usage: 94.00%
2025-06-07T02:19:12.148Z [ERROR] ❌ Error checking slot limits for user 026eaac8-1fa9-449f-a0fd-0f4fd709fbeb: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:19:12.150Z [ERROR] ❌ Error checking slot limits for user 11941296-378f-4c73-8991-038f0c741401: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:19:12.152Z [ERROR] ❌ Error checking slot limits for user 5556e252-f8df-4df2-af35-9bb444117009: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:19:12.155Z [ERROR] ❌ Error checking slot limits for user 59bd3e3f-74d5-4bb3-99c6-c4bccf67b3c2: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:19:12.158Z [ERROR] ❌ Error checking slot limits for user b852e3c3-50a6-45dc-9a77-3ab64dec6936: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:19:12.160Z [ERROR] ❌ Error checking slot limits for user b88465d0-3e6f-4987-b1cb-dd0e566bfa00: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:19:12.162Z [ERROR] ❌ Error checking slot limits for user e631ebe8-4129-4550-9635-6f02860ac6fe: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:19:12.163Z [ERROR] ❌ Error checking slot limits for user f7a3387e-93d9-42a4-b484-c12776bd1d1d: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:24:10.550Z [ERROR] ⚠️  High memory usage: 93.69%
2025-06-07T02:24:12.151Z [ERROR] ❌ Error checking slot limits for user 026eaac8-1fa9-449f-a0fd-0f4fd709fbeb: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:24:12.152Z [ERROR] ❌ Error checking slot limits for user 11941296-378f-4c73-8991-038f0c741401: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:24:12.154Z [ERROR] ❌ Error checking slot limits for user 5556e252-f8df-4df2-af35-9bb444117009: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:24:12.156Z [ERROR] ❌ Error checking slot limits for user 59bd3e3f-74d5-4bb3-99c6-c4bccf67b3c2: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:24:12.159Z [ERROR] ❌ Error checking slot limits for user b852e3c3-50a6-45dc-9a77-3ab64dec6936: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:24:12.161Z [ERROR] ❌ Error checking slot limits for user b88465d0-3e6f-4987-b1cb-dd0e566bfa00: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:24:12.163Z [ERROR] ❌ Error checking slot limits for user e631ebe8-4129-4550-9635-6f02860ac6fe: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:24:12.166Z [ERROR] ❌ Error checking slot limits for user f7a3387e-93d9-42a4-b484-c12776bd1d1d: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:29:10.551Z [ERROR] ⚠️  High memory usage: 95.59%
2025-06-07T02:29:12.162Z [ERROR] ❌ Error checking slot limits for user 026eaac8-1fa9-449f-a0fd-0f4fd709fbeb: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:29:12.165Z [ERROR] ❌ Error checking slot limits for user 11941296-378f-4c73-8991-038f0c741401: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:29:12.167Z [ERROR] ❌ Error checking slot limits for user 5556e252-f8df-4df2-af35-9bb444117009: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:29:12.170Z [ERROR] ❌ Error checking slot limits for user 59bd3e3f-74d5-4bb3-99c6-c4bccf67b3c2: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:29:12.174Z [ERROR] ❌ Error checking slot limits for user b852e3c3-50a6-45dc-9a77-3ab64dec6936: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:29:12.177Z [ERROR] ❌ Error checking slot limits for user b88465d0-3e6f-4987-b1cb-dd0e566bfa00: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:29:12.182Z [ERROR] ❌ Error checking slot limits for user e631ebe8-4129-4550-9635-6f02860ac6fe: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:29:12.185Z [ERROR] ❌ Error checking slot limits for user f7a3387e-93d9-42a4-b484-c12776bd1d1d: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:34:12.163Z [ERROR] ❌ Error checking slot limits for user 026eaac8-1fa9-449f-a0fd-0f4fd709fbeb: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:34:12.165Z [ERROR] ❌ Error checking slot limits for user 11941296-378f-4c73-8991-038f0c741401: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:34:12.168Z [ERROR] ❌ Error checking slot limits for user 5556e252-f8df-4df2-af35-9bb444117009: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:34:12.170Z [ERROR] ❌ Error checking slot limits for user 59bd3e3f-74d5-4bb3-99c6-c4bccf67b3c2: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:34:12.175Z [ERROR] ❌ Error checking slot limits for user b852e3c3-50a6-45dc-9a77-3ab64dec6936: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:34:12.179Z [ERROR] ❌ Error checking slot limits for user b88465d0-3e6f-4987-b1cb-dd0e566bfa00: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:34:12.181Z [ERROR] ❌ Error checking slot limits for user e631ebe8-4129-4550-9635-6f02860ac6fe: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:34:12.183Z [ERROR] ❌ Error checking slot limits for user f7a3387e-93d9-42a4-b484-c12776bd1d1d: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:39:12.168Z [ERROR] ❌ Error checking slot limits for user 026eaac8-1fa9-449f-a0fd-0f4fd709fbeb: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:39:12.171Z [ERROR] ❌ Error checking slot limits for user 11941296-378f-4c73-8991-038f0c741401: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:39:12.173Z [ERROR] ❌ Error checking slot limits for user 5556e252-f8df-4df2-af35-9bb444117009: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:39:12.176Z [ERROR] ❌ Error checking slot limits for user 59bd3e3f-74d5-4bb3-99c6-c4bccf67b3c2: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:39:12.180Z [ERROR] ❌ Error checking slot limits for user b852e3c3-50a6-45dc-9a77-3ab64dec6936: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:39:12.183Z [ERROR] ❌ Error checking slot limits for user b88465d0-3e6f-4987-b1cb-dd0e566bfa00: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:39:12.185Z [ERROR] ❌ Error checking slot limits for user e631ebe8-4129-4550-9635-6f02860ac6fe: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:39:12.187Z [ERROR] ❌ Error checking slot limits for user f7a3387e-93d9-42a4-b484-c12776bd1d1d: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:44:10.552Z [ERROR] ⚠️  High memory usage: 92.85%
2025-06-07T02:44:12.164Z [ERROR] ❌ Error checking slot limits for user 026eaac8-1fa9-449f-a0fd-0f4fd709fbeb: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:44:12.166Z [ERROR] ❌ Error checking slot limits for user 11941296-378f-4c73-8991-038f0c741401: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:44:12.168Z [ERROR] ❌ Error checking slot limits for user 5556e252-f8df-4df2-af35-9bb444117009: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:44:12.170Z [ERROR] ❌ Error checking slot limits for user 59bd3e3f-74d5-4bb3-99c6-c4bccf67b3c2: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:44:12.173Z [ERROR] ❌ Error checking slot limits for user b852e3c3-50a6-45dc-9a77-3ab64dec6936: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:44:12.175Z [ERROR] ❌ Error checking slot limits for user b88465d0-3e6f-4987-b1cb-dd0e566bfa00: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:44:12.176Z [ERROR] ❌ Error checking slot limits for user e631ebe8-4129-4550-9635-6f02860ac6fe: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:44:12.178Z [ERROR] ❌ Error checking slot limits for user f7a3387e-93d9-42a4-b484-c12776bd1d1d: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:49:12.180Z [ERROR] ❌ Error checking slot limits for user 026eaac8-1fa9-449f-a0fd-0f4fd709fbeb: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:49:12.184Z [ERROR] ❌ Error checking slot limits for user 11941296-378f-4c73-8991-038f0c741401: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:49:12.187Z [ERROR] ❌ Error checking slot limits for user 5556e252-f8df-4df2-af35-9bb444117009: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:49:12.193Z [ERROR] ❌ Error checking slot limits for user 59bd3e3f-74d5-4bb3-99c6-c4bccf67b3c2: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:49:12.199Z [ERROR] ❌ Error checking slot limits for user b852e3c3-50a6-45dc-9a77-3ab64dec6936: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:49:12.201Z [ERROR] ❌ Error checking slot limits for user b88465d0-3e6f-4987-b1cb-dd0e566bfa00: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:49:12.203Z [ERROR] ❌ Error checking slot limits for user e631ebe8-4129-4550-9635-6f02860ac6fe: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:49:12.205Z [ERROR] ❌ Error checking slot limits for user f7a3387e-93d9-42a4-b484-c12776bd1d1d: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:54:12.175Z [ERROR] ❌ Error checking slot limits for user 026eaac8-1fa9-449f-a0fd-0f4fd709fbeb: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:54:12.178Z [ERROR] ❌ Error checking slot limits for user 11941296-378f-4c73-8991-038f0c741401: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:54:12.181Z [ERROR] ❌ Error checking slot limits for user 5556e252-f8df-4df2-af35-9bb444117009: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:54:12.185Z [ERROR] ❌ Error checking slot limits for user 59bd3e3f-74d5-4bb3-99c6-c4bccf67b3c2: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:54:12.191Z [ERROR] ❌ Error checking slot limits for user b852e3c3-50a6-45dc-9a77-3ab64dec6936: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:54:12.197Z [ERROR] ❌ Error checking slot limits for user b88465d0-3e6f-4987-b1cb-dd0e566bfa00: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:54:12.199Z [ERROR] ❌ Error checking slot limits for user e631ebe8-4129-4550-9635-6f02860ac6fe: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:54:12.201Z [ERROR] ❌ Error checking slot limits for user f7a3387e-93d9-42a4-b484-c12776bd1d1d: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:59:12.181Z [ERROR] ❌ Error checking slot limits for user 026eaac8-1fa9-449f-a0fd-0f4fd709fbeb: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:59:12.184Z [ERROR] ❌ Error checking slot limits for user 11941296-378f-4c73-8991-038f0c741401: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:59:12.186Z [ERROR] ❌ Error checking slot limits for user 5556e252-f8df-4df2-af35-9bb444117009: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:59:12.189Z [ERROR] ❌ Error checking slot limits for user 59bd3e3f-74d5-4bb3-99c6-c4bccf67b3c2: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:59:12.193Z [ERROR] ❌ Error checking slot limits for user b852e3c3-50a6-45dc-9a77-3ab64dec6936: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:59:12.195Z [ERROR] ❌ Error checking slot limits for user b88465d0-3e6f-4987-b1cb-dd0e566bfa00: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:59:12.198Z [ERROR] ❌ Error checking slot limits for user e631ebe8-4129-4550-9635-6f02860ac6fe: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T02:59:12.200Z [ERROR] ❌ Error checking slot limits for user f7a3387e-93d9-42a4-b484-c12776bd1d1d: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T03:04:12.186Z [ERROR] ❌ Error checking slot limits for user 026eaac8-1fa9-449f-a0fd-0f4fd709fbeb: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T03:04:12.191Z [ERROR] ❌ Error checking slot limits for user 11941296-378f-4c73-8991-038f0c741401: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T03:04:12.194Z [ERROR] ❌ Error checking slot limits for user 5556e252-f8df-4df2-af35-9bb444117009: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T03:04:12.197Z [ERROR] ❌ Error checking slot limits for user 59bd3e3f-74d5-4bb3-99c6-c4bccf67b3c2: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T03:04:12.201Z [ERROR] ❌ Error checking slot limits for user b852e3c3-50a6-45dc-9a77-3ab64dec6936: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T03:04:12.203Z [ERROR] ❌ Error checking slot limits for user b88465d0-3e6f-4987-b1cb-dd0e566bfa00: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T03:04:12.205Z [ERROR] ❌ Error checking slot limits for user e631ebe8-4129-4550-9635-6f02860ac6fe: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T03:04:12.207Z [ERROR] ❌ Error checking slot limits for user f7a3387e-93d9-42a4-b484-c12776bd1d1d: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T03:09:12.187Z [ERROR] ❌ Error checking slot limits for user 026eaac8-1fa9-449f-a0fd-0f4fd709fbeb: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T03:09:12.193Z [ERROR] ❌ Error checking slot limits for user 11941296-378f-4c73-8991-038f0c741401: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T03:09:12.196Z [ERROR] ❌ Error checking slot limits for user 5556e252-f8df-4df2-af35-9bb444117009: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T03:09:12.199Z [ERROR] ❌ Error checking slot limits for user 59bd3e3f-74d5-4bb3-99c6-c4bccf67b3c2: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T03:09:12.202Z [ERROR] ❌ Error checking slot limits for user b852e3c3-50a6-45dc-9a77-3ab64dec6936: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T03:09:12.204Z [ERROR] ❌ Error checking slot limits for user b88465d0-3e6f-4987-b1cb-dd0e566bfa00: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T03:09:12.206Z [ERROR] ❌ Error checking slot limits for user e631ebe8-4129-4550-9635-6f02860ac6fe: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T03:09:12.208Z [ERROR] ❌ Error checking slot limits for user f7a3387e-93d9-42a4-b484-c12776bd1d1d: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T03:14:12.198Z [ERROR] ❌ Error checking slot limits for user 026eaac8-1fa9-449f-a0fd-0f4fd709fbeb: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T03:14:12.200Z [ERROR] ❌ Error checking slot limits for user 11941296-378f-4c73-8991-038f0c741401: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T03:14:12.203Z [ERROR] ❌ Error checking slot limits for user 5556e252-f8df-4df2-af35-9bb444117009: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T03:14:12.205Z [ERROR] ❌ Error checking slot limits for user 59bd3e3f-74d5-4bb3-99c6-c4bccf67b3c2: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T03:14:12.208Z [ERROR] ❌ Error checking slot limits for user b852e3c3-50a6-45dc-9a77-3ab64dec6936: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T03:14:12.211Z [ERROR] ❌ Error checking slot limits for user b88465d0-3e6f-4987-b1cb-dd0e566bfa00: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T03:14:12.212Z [ERROR] ❌ Error checking slot limits for user e631ebe8-4129-4550-9635-6f02860ac6fe: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T03:14:12.214Z [ERROR] ❌ Error checking slot limits for user f7a3387e-93d9-42a4-b484-c12776bd1d1d: TypeError: Stream.findActiveByUserId is not a function
    at Subscription.enforceSlotLimits (/home/<USER>/streamonpod/models/Subscription.js:312:48)
    at async SubscriptionMonitor.enforceSlotLimits (/home/<USER>/streamonpod/services/subscriptionMonitor.js:132:22)
    at async SubscriptionMonitor.performCheck (/home/<USER>/streamonpod/services/subscriptionMonitor.js:81:7)
2025-06-07T03:15:38.723Z [ERROR] 🚨 [ERROR] {
  "errorId": "df2a61c3-f0dc-4c2e-b3f6-8337047b76a0",
  "timestamp": "2025-06-07T03:15:38.723Z",
  "type": "STREAMING_ERROR",
  "message": "Stream is already active",
  "statusCode": 500,
  "streamId": "d45276d0-9370-4018-982a-2880deb151f1",
  "operation": "startStream",
  "context": {
    "streamId": "d45276d0-9370-4018-982a-2880deb151f1",
    "operation": "start"
  }
}
2025-06-07T03:20:23.424Z [ERROR] ⚠️  High memory usage: 95.63%
2025-06-07T03:25:23.424Z [ERROR] ⚠️  High memory usage: 94.46%
2025-06-07T03:30:23.425Z [ERROR] ⚠️  High memory usage: 90.79%
2025-06-07T03:45:23.425Z [ERROR] ⚠️  High memory usage: 91.99%
2025-06-07T04:05:23.425Z [ERROR] ⚠️  High memory usage: 92.23%
2025-06-07T04:30:23.428Z [ERROR] ⚠️  High memory usage: 91.08%
2025-06-07T05:03:38.186Z [ERROR] ⚠️  High memory usage: 93.01%
2025-06-07T05:28:38.187Z [ERROR] ⚠️  High memory usage: 93.34%
2025-06-07T05:33:38.190Z [ERROR] ⚠️  High memory usage: 91.70%
2025-06-07T05:43:38.191Z [ERROR] ⚠️  High memory usage: 91.10%
2025-06-07T06:13:38.191Z [ERROR] ⚠️  High memory usage: 93.31%
2025-06-07T07:34:12.679Z [ERROR] Error updating stream: TypeError: datetimeLocalToUTC is not a function
    at /home/<USER>/streamonpod/app.js:3770:31
2025-06-07T07:34:18.199Z [ERROR] Error updating stream: TypeError: datetimeLocalToUTC is not a function
    at /home/<USER>/streamonpod/app.js:3770:31
2025-06-07T07:34:21.193Z [ERROR] Error updating stream: TypeError: datetimeLocalToUTC is not a function
    at /home/<USER>/streamonpod/app.js:3770:31
2025-06-07T07:34:21.915Z [ERROR] Error updating stream: TypeError: datetimeLocalToUTC is not a function
    at /home/<USER>/streamonpod/app.js:3770:31
2025-06-07T07:34:35.030Z [ERROR] Error updating stream: TypeError: datetimeLocalToUTC is not a function
    at /home/<USER>/streamonpod/app.js:3770:31
2025-06-07T07:34:36.769Z [ERROR] Error updating stream: TypeError: datetimeLocalToUTC is not a function
    at /home/<USER>/streamonpod/app.js:3770:31
2025-06-07T07:35:13.731Z [ERROR] Error creating stream: TypeError: datetimeLocalToUTC is not a function
    at /home/<USER>/streamonpod/app.js:3484:31
2025-06-07T08:12:10.154Z [ERROR] ⚠️  High memory usage: 90.72%
2025-06-07T08:33:27.197Z [ERROR] [FFMPEG_STDERR] c58e0b2a-b090-4071-8e8e-89a8623608dc: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/aueh-wzh6-6ba7-geu7-3vdg: Broken pipe
2025-06-07T08:33:27.213Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream c58e0b2a-b090-4071-8e8e-89a8623608dc
2025-06-07T08:47:10.157Z [ERROR] ⚠️  High memory usage: 91.63%
2025-06-07T09:27:10.163Z [ERROR] ⚠️  High memory usage: 90.37%
2025-06-07T10:17:10.168Z [ERROR] ⚠️  High memory usage: 92.31%
2025-06-07T10:57:10.168Z [ERROR] ⚠️  High memory usage: 90.73%
2025-06-07T11:49:39.270Z [ERROR] [FFMPEG_STDERR] 278be0d6-7886-40f6-9e5b-3d2196edc0ee: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/2dsq-z2qf-9s9e-wus4-30ex: Broken pipe
2025-06-07T11:49:39.289Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 278be0d6-7886-40f6-9e5b-3d2196edc0ee
2025-06-07T12:52:10.177Z [ERROR] ⚠️  High memory usage: 91.63%
2025-06-07T13:57:10.181Z [ERROR] ⚠️  High memory usage: 93.26%
2025-06-07T14:32:10.184Z [ERROR] ⚠️  High memory usage: 92.01%
2025-06-07T14:47:10.185Z [ERROR] ⚠️  High memory usage: 90.82%
2025-06-07T14:57:10.187Z [ERROR] ⚠️  High memory usage: 92.14%
2025-06-07T15:12:10.189Z [ERROR] ⚠️  High memory usage: 92.20%
2025-06-07T15:52:10.191Z [ERROR] ⚠️  High memory usage: 90.24%
2025-06-07T16:51:22.447Z [ERROR] 🚨 [ERROR] {
  "errorId": "1acd764b-658f-4aac-ae89-5e2bc4334e7f",
  "timestamp": "2025-06-07T16:51:22.446Z",
  "type": "STREAMING_ERROR",
  "message": "Stream is already active",
  "statusCode": 500,
  "streamId": "278be0d6-7886-40f6-9e5b-3d2196edc0ee",
  "operation": "startStream",
  "context": {
    "streamId": "278be0d6-7886-40f6-9e5b-3d2196edc0ee",
    "operation": "start"
  }
}
2025-06-07T16:51:28.937Z [ERROR] 🚨 [ERROR] {
  "errorId": "15ee70f8-0550-405a-9217-1f73fede5e52",
  "timestamp": "2025-06-07T16:51:28.936Z",
  "type": "STREAMING_ERROR",
  "message": "Stream is already active",
  "statusCode": 500,
  "streamId": "d45276d0-9370-4018-982a-2880deb151f1",
  "operation": "startStream",
  "context": {
    "streamId": "d45276d0-9370-4018-982a-2880deb151f1",
    "operation": "start"
  }
}
2025-06-07T17:29:49.417Z [ERROR] ⚠️  High memory usage: 90.50%
2025-06-07T17:34:49.418Z [ERROR] ⚠️  High memory usage: 92.12%
2025-06-07T17:39:49.419Z [ERROR] ⚠️  High memory usage: 93.79%
2025-06-07T17:44:49.419Z [ERROR] ⚠️  High memory usage: 94.62%
2025-06-07T18:02:13.271Z [ERROR] ⚠️  High memory usage: 90.85%
2025-06-07T18:07:13.272Z [ERROR] ⚠️  High memory usage: 90.54%
2025-06-07T18:27:13.274Z [ERROR] ⚠️  High memory usage: 90.11%
2025-06-07T21:12:13.285Z [ERROR] ⚠️  High memory usage: 92.82%
2025-06-07T22:27:13.289Z [ERROR] ⚠️  High memory usage: 91.56%
2025-06-07T23:37:13.296Z [ERROR] ⚠️  High memory usage: 90.79%
2025-06-08T00:07:13.298Z [ERROR] ⚠️  High memory usage: 91.71%
2025-06-08T02:12:13.311Z [ERROR] ⚠️  High memory usage: 91.69%
2025-06-08T02:17:13.311Z [ERROR] ⚠️  High memory usage: 91.39%
2025-06-08T02:22:13.311Z [ERROR] ⚠️  High memory usage: 94.88%
2025-06-08T02:37:13.310Z [ERROR] ⚠️  High memory usage: 92.50%
2025-06-08T02:47:13.312Z [ERROR] ⚠️  High memory usage: 90.98%
2025-06-08T02:52:13.313Z [ERROR] ⚠️  High memory usage: 93.30%
2025-06-08T02:57:13.313Z [ERROR] ⚠️  High memory usage: 92.46%
2025-06-08T03:27:13.318Z [ERROR] ⚠️  High memory usage: 90.51%
2025-06-08T03:42:13.320Z [ERROR] ⚠️  High memory usage: 91.23%
2025-06-08T06:42:13.335Z [ERROR] ⚠️  High memory usage: 90.40%
2025-06-08T07:27:13.338Z [ERROR] ⚠️  High memory usage: 90.72%
2025-06-08T08:02:13.340Z [ERROR] ⚠️  High memory usage: 91.47%
2025-06-08T09:07:13.346Z [ERROR] ⚠️  High memory usage: 90.52%
2025-06-08T13:37:13.368Z [ERROR] ⚠️  High memory usage: 90.30%
2025-06-08T13:44:43.696Z [ERROR] [StreamingService] Failed to save stream history: TypeError: Cannot read properties of undefined (reading 'start_time')
    at saveStreamHistory (/home/<USER>/streamonpod/services/streamingService.js:1541:17)
    at Object.stopStream (/home/<USER>/streamonpod/services/streamingService.js:1164:13)
    at async /home/<USER>/streamonpod/app.js:3996:24
2025-06-08T13:47:13.369Z [ERROR] ⚠️  High memory usage: 93.72%
2025-06-08T15:19:06.839Z [ERROR] Error fetching current UTC time: connect ETIMEDOUT **************:443
2025-06-08T15:52:13.328Z [ERROR] [StreamingService] Failed to save stream history: TypeError: Cannot read properties of undefined (reading 'start_time')
    at saveStreamHistory (/home/<USER>/streamonpod/services/streamingService.js:1541:17)
    at Object.stopStream (/home/<USER>/streamonpod/services/streamingService.js:1164:13)
    at async /home/<USER>/streamonpod/app.js:3996:24
2025-06-08T15:57:13.380Z [ERROR] ⚠️  High memory usage: 92.63%
2025-06-08T17:02:13.385Z [ERROR] ⚠️  High memory usage: 92.60%
2025-06-08T23:21:39.350Z [ERROR] [FFMPEG_STDERR] d45276d0-9370-4018-982a-2880deb151f1: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/jx8r-02mb-vf09-uhge-a5xx: Connection reset by peer
2025-06-08T23:21:39.380Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream d45276d0-9370-4018-982a-2880deb151f1
2025-06-09T00:57:13.429Z [ERROR] ⚠️  High memory usage: 94.41%
2025-06-09T01:17:13.430Z [ERROR] ⚠️  High memory usage: 90.50%
2025-06-09T01:32:13.431Z [ERROR] ⚠️  High memory usage: 90.05%
2025-06-09T01:47:13.432Z [ERROR] ⚠️  High memory usage: 90.63%
2025-06-09T02:37:13.435Z [ERROR] ⚠️  High memory usage: 90.57%
2025-06-09T06:17:13.449Z [ERROR] ⚠️  High memory usage: 90.25%
2025-06-09T07:42:13.458Z [ERROR] ⚠️  High memory usage: 90.32%
2025-06-09T08:07:13.460Z [ERROR] ⚠️  High memory usage: 90.66%
2025-06-09T08:17:13.462Z [ERROR] ⚠️  High memory usage: 92.84%
2025-06-09T08:27:13.463Z [ERROR] ⚠️  High memory usage: 93.39%
2025-06-09T08:47:13.465Z [ERROR] ⚠️  High memory usage: 94.18%
2025-06-09T09:17:13.468Z [ERROR] ⚠️  High memory usage: 90.57%
2025-06-09T09:37:13.471Z [ERROR] ⚠️  High memory usage: 93.16%
2025-06-09T09:52:13.470Z [ERROR] ⚠️  High memory usage: 90.04%
2025-06-09T09:57:13.471Z [ERROR] ⚠️  High memory usage: 91.76%
2025-06-09T10:02:13.472Z [ERROR] ⚠️  High memory usage: 94.18%
2025-06-09T10:12:13.472Z [ERROR] ⚠️  High memory usage: 91.24%
2025-06-09T10:47:13.473Z [ERROR] ⚠️  High memory usage: 92.53%
2025-06-09T10:57:13.474Z [ERROR] ⚠️  High memory usage: 90.21%
2025-06-09T13:17:30.112Z [ERROR] Error fetching current UTC time: Client network socket disconnected before secure TLS connection was established
2025-06-09T13:27:13.487Z [ERROR] ⚠️  High memory usage: 91.87%
2025-06-09T14:11:10.172Z [ERROR] [FFMPEG_STDERR] d45276d0-9370-4018-982a-2880deb151f1: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/jx8r-02mb-vf09-uhge-a5xx: Broken pipe
2025-06-09T14:11:10.199Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream d45276d0-9370-4018-982a-2880deb151f1
2025-06-09T14:18:57.600Z [ERROR] [FFMPEG_STDERR] 36c02a96-a2c5-4178-a9c3-f86857f0cae8: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/k94v-0t40-j40u-4a3q-dcze: End of file
2025-06-09T14:18:57.618Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 36c02a96-a2c5-4178-a9c3-f86857f0cae8
2025-06-09T14:19:32.281Z [ERROR] [FFMPEG_STDERR] 36c02a96-a2c5-4178-a9c3-f86857f0cae8: rtmp://a.rtmp.youtube.com/live2/k94v-0t40-j40u-4a3q-dcze: Input/output error
2025-06-09T14:19:32.289Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 36c02a96-a2c5-4178-a9c3-f86857f0cae8
2025-06-09T14:19:36.094Z [ERROR] [FFMPEG_STDERR] 36c02a96-a2c5-4178-a9c3-f86857f0cae8: rtmp://a.rtmp.youtube.com/live2/k94v-0t40-j40u-4a3q-dcze: Input/output error
2025-06-09T14:19:36.117Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 36c02a96-a2c5-4178-a9c3-f86857f0cae8
2025-06-09T14:19:39.873Z [ERROR] [FFMPEG_STDERR] 36c02a96-a2c5-4178-a9c3-f86857f0cae8: rtmp://a.rtmp.youtube.com/live2/k94v-0t40-j40u-4a3q-dcze: Input/output error
2025-06-09T14:19:39.880Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 36c02a96-a2c5-4178-a9c3-f86857f0cae8
2025-06-09T14:19:43.622Z [ERROR] [FFMPEG_STDERR] 36c02a96-a2c5-4178-a9c3-f86857f0cae8: rtmp://a.rtmp.youtube.com/live2/k94v-0t40-j40u-4a3q-dcze: Input/output error
2025-06-09T14:19:43.629Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 36c02a96-a2c5-4178-a9c3-f86857f0cae8
2025-06-09T15:37:13.497Z [ERROR] ⚠️  High memory usage: 91.74%
2025-06-09T15:52:13.498Z [ERROR] ⚠️  High memory usage: 91.23%
2025-06-09T15:57:13.499Z [ERROR] ⚠️  High memory usage: 92.40%
2025-06-09T16:37:13.504Z [ERROR] ⚠️  High memory usage: 91.16%
2025-06-09T17:07:13.504Z [ERROR] ⚠️  High memory usage: 91.41%
2025-06-09T17:27:13.507Z [ERROR] ⚠️  High memory usage: 91.97%
2025-06-09T17:47:13.509Z [ERROR] ⚠️  High memory usage: 91.76%
2025-06-09T18:12:13.509Z [ERROR] ⚠️  High memory usage: 92.37%
2025-06-09T18:37:13.512Z [ERROR] ⚠️  High memory usage: 92.01%
2025-06-09T18:57:13.513Z [ERROR] ⚠️  High memory usage: 90.42%
2025-06-09T19:02:13.515Z [ERROR] ⚠️  High memory usage: 91.22%
2025-06-09T19:27:13.516Z [ERROR] ⚠️  High memory usage: 90.89%
2025-06-09T19:30:14.984Z [ERROR] [FFMPEG_STDERR] 93c36846-e276-4ecf-b0b3-ddbfb27e2c8e: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/0zuq-cxu5-0yy0-s2z5-6bdz: Connection reset by peer
2025-06-09T19:30:15.005Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 93c36846-e276-4ecf-b0b3-ddbfb27e2c8e
2025-06-09T19:39:42.235Z [ERROR] [FFMPEG_STDERR] d45276d0-9370-4018-982a-2880deb151f1: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/jx8r-02mb-vf09-uhge-a5xx: Broken pipe
2025-06-09T19:39:42.256Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream d45276d0-9370-4018-982a-2880deb151f1
2025-06-09T19:57:13.519Z [ERROR] ⚠️  High memory usage: 90.13%
2025-06-09T20:22:13.520Z [ERROR] ⚠️  High memory usage: 91.55%
2025-06-09T20:52:13.522Z [ERROR] ⚠️  High memory usage: 90.08%
2025-06-09T21:17:13.522Z [ERROR] ⚠️  High memory usage: 90.25%
2025-06-09T21:47:13.525Z [ERROR] ⚠️  High memory usage: 91.80%
2025-06-09T22:12:13.527Z [ERROR] ⚠️  High memory usage: 91.40%
2025-06-09T22:37:13.529Z [ERROR] ⚠️  High memory usage: 92.20%
2025-06-09T23:12:13.533Z [ERROR] ⚠️  High memory usage: 90.66%
2025-06-10T00:37:13.543Z [ERROR] ⚠️  High memory usage: 92.00%
2025-06-10T00:42:13.543Z [ERROR] ⚠️  High memory usage: 92.25%
2025-06-10T04:37:13.564Z [ERROR] ⚠️  High memory usage: 91.29%
2025-06-10T05:02:13.566Z [ERROR] ⚠️  High memory usage: 91.11%
2025-06-10T05:52:13.572Z [ERROR] ⚠️  High memory usage: 90.30%
2025-06-10T06:17:13.574Z [ERROR] ⚠️  High memory usage: 92.42%
2025-06-10T06:32:13.576Z [ERROR] ⚠️  High memory usage: 92.91%
2025-06-10T06:37:13.577Z [ERROR] ⚠️  High memory usage: 94.12%
2025-06-10T06:52:13.577Z [ERROR] ⚠️  High memory usage: 92.55%
2025-06-10T07:17:13.581Z [ERROR] ⚠️  High memory usage: 91.01%
2025-06-10T07:22:13.582Z [ERROR] ⚠️  High memory usage: 92.79%
2025-06-10T07:37:13.583Z [ERROR] ⚠️  High memory usage: 92.67%
2025-06-10T07:52:13.587Z [ERROR] ⚠️  High memory usage: 90.29%
2025-06-10T11:42:13.604Z [ERROR] ⚠️  High memory usage: 90.21%
2025-06-10T11:52:13.604Z [ERROR] ⚠️  High memory usage: 93.67%
2025-06-10T12:02:13.604Z [ERROR] ⚠️  High memory usage: 90.33%
2025-06-10T12:12:13.607Z [ERROR] ⚠️  High memory usage: 90.77%
2025-06-10T12:22:13.607Z [ERROR] ⚠️  High memory usage: 92.95%
2025-06-10T12:27:13.608Z [ERROR] ⚠️  High memory usage: 91.33%
2025-06-10T12:32:13.608Z [ERROR] ⚠️  High memory usage: 93.08%
2025-06-10T12:42:13.609Z [ERROR] ⚠️  High memory usage: 93.04%
2025-06-10T12:57:13.611Z [ERROR] ⚠️  High memory usage: 90.95%
2025-06-10T13:22:13.614Z [ERROR] ⚠️  High memory usage: 90.21%
2025-06-10T13:47:13.614Z [ERROR] ⚠️  High memory usage: 91.19%
2025-06-10T14:07:13.616Z [ERROR] ⚠️  High memory usage: 90.50%
2025-06-10T14:12:13.616Z [ERROR] ⚠️  High memory usage: 93.54%
2025-06-10T14:37:13.619Z [ERROR] ⚠️  High memory usage: 91.64%
2025-06-10T14:42:13.620Z [ERROR] ⚠️  High memory usage: 92.15%
2025-06-10T16:22:13.628Z [ERROR] ⚠️  High memory usage: 90.34%
2025-06-10T16:47:13.630Z [ERROR] ⚠️  High memory usage: 90.35%
2025-06-10T18:45:57.728Z [ERROR] [FFMPEG_STDERR] 278be0d6-7886-40f6-9e5b-3d2196edc0ee: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/2dsq-z2qf-9s9e-wus4-30ex: Broken pipe
2025-06-10T18:45:57.728Z [ERROR] [FFMPEG_STDERR] 449009a0-d9a4-4579-9979-c776bd09d023: av_interleaved_write_frame(): Broken pipe
Error writing trailer of rtmp://a.rtmp.youtube.com/live2/81f6-b1h7-m4tx-93pt-571u: Broken pipe
2025-06-10T18:45:57.728Z [ERROR] [FFMPEG_STDERR] d45276d0-9370-4018-982a-2880deb151f1: av_interleaved_write_frame(): Broken pipe
Error writing trailer of rtmp://a.rtmp.youtube.com/live2/jx8r-02mb-vf09-uhge-a5xx: Broken pipe
2025-06-10T18:45:57.729Z [ERROR] [FFMPEG_STDERR] 98022af8-7561-4b99-bd77-39462cfa803a: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/jx8r-02mb-vf09-uhge-a5xx: Broken pipe
2025-06-10T18:45:57.748Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 449009a0-d9a4-4579-9979-c776bd09d023
2025-06-10T18:45:57.750Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream d45276d0-9370-4018-982a-2880deb151f1
2025-06-10T18:45:57.750Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 278be0d6-7886-40f6-9e5b-3d2196edc0ee
2025-06-10T18:45:57.750Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 98022af8-7561-4b99-bd77-39462cfa803a
2025-06-10T18:59:20.129Z [ERROR] [FFMPEG_STDERR] 93c36846-e276-4ecf-b0b3-ddbfb27e2c8e: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/0zuq-cxu5-0yy0-s2z5-6bdz: Connection reset by peer
2025-06-10T18:59:20.174Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 93c36846-e276-4ecf-b0b3-ddbfb27e2c8e
2025-06-10T19:12:13.644Z [ERROR] ⚠️  High memory usage: 91.16%
2025-06-10T22:42:13.655Z [ERROR] ⚠️  High memory usage: 91.85%
2025-06-10T22:47:13.656Z [ERROR] ⚠️  High memory usage: 92.21%
2025-06-11T00:17:13.663Z [ERROR] ⚠️  High memory usage: 90.02%
2025-06-11T05:47:13.697Z [ERROR] ⚠️  High memory usage: 90.76%
2025-06-11T06:22:13.703Z [ERROR] ⚠️  High memory usage: 90.53%
2025-06-11T07:07:13.711Z [ERROR] ⚠️  High memory usage: 90.56%
2025-06-11T07:12:13.711Z [ERROR] ⚠️  High memory usage: 91.82%
2025-06-11T07:17:13.710Z [ERROR] ⚠️  High memory usage: 92.28%
2025-06-11T08:42:13.715Z [ERROR] ⚠️  High memory usage: 90.15%
2025-06-11T13:37:09.019Z [ERROR] [FFMPEG_STDERR] d45276d0-9370-4018-982a-2880deb151f1: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/jx8r-02mb-vf09-uhge-a5xx: Broken pipe
2025-06-11T13:37:09.038Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream d45276d0-9370-4018-982a-2880deb151f1
2025-06-11T18:26:41.802Z [ERROR] [FFMPEG_STDERR] d45276d0-9370-4018-982a-2880deb151f1: av_interleaved_write_frame(): End of file
Error writing trailer of rtmp://a.rtmp.youtube.com/live2/jx8r-02mb-vf09-uhge-a5xx: End of file
2025-06-11T18:26:41.824Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream d45276d0-9370-4018-982a-2880deb151f1
2025-06-11T18:26:41.861Z [ERROR] [FFMPEG_STDERR] d45276d0-9370-4018-982a-2880deb151f1: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/jx8r-02mb-vf09-uhge-a5xx: End of file
2025-06-11T18:26:41.879Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream d45276d0-9370-4018-982a-2880deb151f1
2025-06-11T18:26:42.248Z [ERROR] [FFMPEG_STDERR] 0c1aa411-c9b7-4826-875f-11aa850d55aa: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/e0q7-kmu5-vtsu-z1rw-0m1z: Broken pipe
2025-06-11T18:26:42.272Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 0c1aa411-c9b7-4826-875f-11aa850d55aa
2025-06-11T18:26:51.629Z [ERROR] [FFMPEG_STDERR] c7f89428-464a-430c-8631-6b184c5465c4: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/0zuq-cxu5-0yy0-s2z5-6bdz: Connection reset by peer
2025-06-11T18:26:51.630Z [ERROR] [FFMPEG_STDERR] 72af7e7b-f497-4ed9-a0c5-a9a3c96667a6: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/5fg7-3u6b-eq9v-kyb2-35v8: Connection reset by peer
2025-06-11T18:26:51.631Z [ERROR] [FFMPEG_STDERR] 1168581d-fa9b-4701-bdea-c995c9f9de88: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/mptg-p8v2-vc4t-kp2g-3yfc: Connection reset by peer
2025-06-11T18:26:51.647Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 72af7e7b-f497-4ed9-a0c5-a9a3c96667a6
2025-06-11T18:26:51.647Z [ERROR] [FFMPEG_STDERR] 023f6130-8386-46d1-8a79-0389e2bd5d6f: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/7c02-u19u-2pwj-vc92-3c7a: Connection reset by peer
2025-06-11T18:26:51.659Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream c7f89428-464a-430c-8631-6b184c5465c4
2025-06-11T18:26:51.659Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 1168581d-fa9b-4701-bdea-c995c9f9de88
2025-06-11T18:26:51.665Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 023f6130-8386-46d1-8a79-0389e2bd5d6f
2025-06-11T18:26:51.889Z [ERROR] [FFMPEG_STDERR] 98022af8-7561-4b99-bd77-39462cfa803a: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/jx8r-02mb-vf09-uhge-a5xx: Connection reset by peer
2025-06-11T18:26:51.911Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 98022af8-7561-4b99-bd77-39462cfa803a
2025-06-11T18:26:52.398Z [ERROR] [FFMPEG_STDERR] 25b553e9-7001-420b-8752-f3fb900fd654: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/murk-epb3-1gjj-v5df-9h70: Connection reset by peer
2025-06-11T18:26:52.413Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25b553e9-7001-420b-8752-f3fb900fd654
2025-06-11T18:26:53.342Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-11T18:26:53.355Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-11T18:29:34.699Z [ERROR] 🚨 [ERROR] {
  "errorId": "24b7faa9-afde-448c-8d99-0f1a0bfa9dd2",
  "timestamp": "2025-06-11T18:29:34.699Z",
  "type": "STREAMING_ERROR",
  "message": "Stream is already active",
  "statusCode": 500,
  "streamId": "8a410b2a-fbcb-46a6-b710-9bdd0036be5b",
  "operation": "startStream",
  "context": {
    "streamId": "8a410b2a-fbcb-46a6-b710-9bdd0036be5b",
    "operation": "start"
  }
}
2025-06-11T18:32:13.760Z [ERROR] ⚠️  High memory usage: 90.65%
2025-06-11T18:37:13.760Z [ERROR] ⚠️  High memory usage: 91.11%
2025-06-11T18:42:13.759Z [ERROR] ⚠️  High memory usage: 92.45%
2025-06-11T18:47:13.759Z [ERROR] ⚠️  High memory usage: 93.64%
2025-06-11T18:52:13.760Z [ERROR] ⚠️  High memory usage: 93.17%
2025-06-11T18:57:13.760Z [ERROR] ⚠️  High memory usage: 93.03%
2025-06-11T19:02:13.761Z [ERROR] ⚠️  High memory usage: 92.21%
2025-06-11T19:07:13.761Z [ERROR] ⚠️  High memory usage: 93.91%
2025-06-11T19:12:13.762Z [ERROR] ⚠️  High memory usage: 93.21%
2025-06-11T19:17:13.762Z [ERROR] ⚠️  High memory usage: 93.51%
2025-06-11T19:22:13.763Z [ERROR] ⚠️  High memory usage: 93.00%
2025-06-11T20:25:15.814Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: End of file
2025-06-11T20:25:15.834Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-11T20:25:16.898Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-11T20:25:16.917Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-11T20:25:16.985Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-11T20:25:17.000Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-11T20:25:17.558Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-11T20:25:17.575Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-11T20:25:17.576Z [ERROR] [StreamingService] Maximum retry attempts (3) reached for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-11T20:25:17.671Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-11T20:25:17.685Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-11T20:25:18.208Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-11T20:25:18.224Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-11T20:25:18.225Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: av_interleaved_write_frame(): Broken pipe
Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-11T20:25:18.238Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-11T20:25:18.839Z [ERROR] 🚨 [ERROR] {
  "errorId": "b832a0c5-d385-4b9a-b431-a4ca6d22a67a",
  "timestamp": "2025-06-11T20:25:18.838Z",
  "type": "STREAMING_ERROR",
  "message": "Stream is temporarily disabled due to repeated failures. Please check your RTMP settings.",
  "statusCode": 500,
  "streamId": "8a410b2a-fbcb-46a6-b710-9bdd0036be5b",
  "operation": "startStream",
  "context": {
    "streamId": "8a410b2a-fbcb-46a6-b710-9bdd0036be5b",
    "operation": "start"
  }
}
2025-06-11T20:25:18.839Z [ERROR] [StreamingService] Failed to restart stream: Stream is temporarily disabled due to repeated failures. Please check your RTMP settings.
2025-06-11T20:25:19.584Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-11T20:25:19.599Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-11T20:25:19.918Z [ERROR] 🚨 [ERROR] {
  "errorId": "9cecd6c7-ec69-442f-859e-ea23a41283bc",
  "timestamp": "2025-06-11T20:25:19.918Z",
  "type": "STREAMING_ERROR",
  "message": "Stream is temporarily disabled due to repeated failures. Please check your RTMP settings.",
  "statusCode": 500,
  "streamId": "8a410b2a-fbcb-46a6-b710-9bdd0036be5b",
  "operation": "startStream",
  "context": {
    "streamId": "8a410b2a-fbcb-46a6-b710-9bdd0036be5b",
    "operation": "start"
  }
}
2025-06-11T20:25:19.918Z [ERROR] [StreamingService] Failed to restart stream: Stream is temporarily disabled due to repeated failures. Please check your RTMP settings.
2025-06-11T20:25:20.002Z [ERROR] 🚨 [ERROR] {
  "errorId": "0a6a45ba-18e8-4e58-9811-5be7e25842bb",
  "timestamp": "2025-06-11T20:25:20.002Z",
  "type": "STREAMING_ERROR",
  "message": "Stream is temporarily disabled due to repeated failures. Please check your RTMP settings.",
  "statusCode": 500,
  "streamId": "8a410b2a-fbcb-46a6-b710-9bdd0036be5b",
  "operation": "startStream",
  "context": {
    "streamId": "8a410b2a-fbcb-46a6-b710-9bdd0036be5b",
    "operation": "start"
  }
}
2025-06-11T20:25:20.002Z [ERROR] [StreamingService] Failed to restart stream: Stream is temporarily disabled due to repeated failures. Please check your RTMP settings.
2025-06-11T20:26:55.527Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-11T20:26:55.544Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-11T20:26:55.663Z [ERROR] [FFMPEG_STDERR] 25b553e9-7001-420b-8752-f3fb900fd654: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/murk-epb3-1gjj-v5df-9h70: Broken pipe
2025-06-11T20:26:55.682Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 25b553e9-7001-420b-8752-f3fb900fd654
2025-06-11T20:57:13.771Z [ERROR] ⚠️  High memory usage: 90.80%
2025-06-11T21:17:13.772Z [ERROR] ⚠️  High memory usage: 93.40%
2025-06-11T21:22:13.772Z [ERROR] ⚠️  High memory usage: 93.69%
2025-06-11T21:26:22.898Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-11T21:26:22.920Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-11T21:26:24.380Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-11T21:26:24.400Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-11T21:26:26.982Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-11T21:26:26.999Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-11T21:26:30.003Z [ERROR] 🚨 [ERROR] {
  "errorId": "f5936a52-9030-4e77-8d2f-5157c3eba779",
  "timestamp": "2025-06-11T21:26:30.003Z",
  "type": "STREAMING_ERROR",
  "message": "Stream is already active",
  "statusCode": 500,
  "streamId": "8a410b2a-fbcb-46a6-b710-9bdd0036be5b",
  "operation": "startStream",
  "context": {
    "streamId": "8a410b2a-fbcb-46a6-b710-9bdd0036be5b",
    "operation": "start"
  }
}
2025-06-11T21:26:30.004Z [ERROR] [StreamingService] Failed to restart stream: Stream is already active
2025-06-11T21:27:13.773Z [ERROR] ⚠️  High memory usage: 91.32%
2025-06-11T21:32:13.773Z [ERROR] ⚠️  High memory usage: 93.08%
2025-06-11T21:44:27.742Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-11T21:44:27.743Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-11T21:44:27.743Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-11T21:44:27.746Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-11T21:44:27.747Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-11T21:44:27.751Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-11T21:44:27.774Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-11T21:44:27.777Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-11T21:44:27.778Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-11T21:44:27.779Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-11T21:44:27.779Z [ERROR] [StreamingService] Maximum retry attempts (3) reached for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-11T21:44:27.799Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-11T21:44:27.800Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-11T21:44:30.779Z [ERROR] 🚨 [ERROR] {
  "errorId": "b7f54258-4039-4b32-b1bb-0d15c350ec6e",
  "timestamp": "2025-06-11T21:44:30.779Z",
  "type": "STREAMING_ERROR",
  "message": "Stream is temporarily disabled due to repeated failures. Please check your RTMP settings.",
  "statusCode": 500,
  "streamId": "8a410b2a-fbcb-46a6-b710-9bdd0036be5b",
  "operation": "startStream",
  "context": {
    "streamId": "8a410b2a-fbcb-46a6-b710-9bdd0036be5b",
    "operation": "start"
  }
}
2025-06-11T21:44:30.779Z [ERROR] [StreamingService] Failed to restart stream: Stream is temporarily disabled due to repeated failures. Please check your RTMP settings.
2025-06-11T21:44:30.783Z [ERROR] 🚨 [ERROR] {
  "errorId": "bb38fde2-5f00-489c-8ed4-3b6587547f28",
  "timestamp": "2025-06-11T21:44:30.783Z",
  "type": "STREAMING_ERROR",
  "message": "Stream is temporarily disabled due to repeated failures. Please check your RTMP settings.",
  "statusCode": 500,
  "streamId": "8a410b2a-fbcb-46a6-b710-9bdd0036be5b",
  "operation": "startStream",
  "context": {
    "streamId": "8a410b2a-fbcb-46a6-b710-9bdd0036be5b",
    "operation": "start"
  }
}
2025-06-11T21:44:30.783Z [ERROR] [StreamingService] Failed to restart stream: Stream is temporarily disabled due to repeated failures. Please check your RTMP settings.
2025-06-11T21:44:30.785Z [ERROR] 🚨 [ERROR] {
  "errorId": "18db2502-fd43-45d3-80d0-ef8d480faaa3",
  "timestamp": "2025-06-11T21:44:30.785Z",
  "type": "STREAMING_ERROR",
  "message": "Stream is temporarily disabled due to repeated failures. Please check your RTMP settings.",
  "statusCode": 500,
  "streamId": "8a410b2a-fbcb-46a6-b710-9bdd0036be5b",
  "operation": "startStream",
  "context": {
    "streamId": "8a410b2a-fbcb-46a6-b710-9bdd0036be5b",
    "operation": "start"
  }
}
2025-06-11T21:44:30.785Z [ERROR] [StreamingService] Failed to restart stream: Stream is temporarily disabled due to repeated failures. Please check your RTMP settings.
2025-06-11T21:52:13.777Z [ERROR] ⚠️  High memory usage: 92.30%
2025-06-11T22:01:44.039Z [ERROR] [FFMPEG_STDERR] c3b30d94-1d41-4834-b8f7-22d1224c1caf: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/0jqm-ys4c-wqbm-t1b0-fsq0: Broken pipe
2025-06-11T22:01:44.070Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream c3b30d94-1d41-4834-b8f7-22d1224c1caf
2025-06-11T22:07:13.777Z [ERROR] ⚠️  High memory usage: 91.71%
2025-06-11T22:12:13.777Z [ERROR] ⚠️  High memory usage: 90.89%
2025-06-11T22:34:01.844Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-11T22:34:01.867Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-11T22:34:04.860Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-11T22:34:04.883Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-11T22:34:06.687Z [ERROR] [FFMPEG_STDERR] d45276d0-9370-4018-982a-2880deb151f1: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/jx8r-02mb-vf09-uhge-a5xx: Broken pipe
2025-06-11T22:34:06.703Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream d45276d0-9370-4018-982a-2880deb151f1
2025-06-11T22:34:07.309Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-11T22:34:07.323Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-11T22:34:07.401Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-11T22:34:07.416Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-11T22:34:07.804Z [ERROR] [FFMPEG_STDERR] 278be0d6-7886-40f6-9e5b-3d2196edc0ee: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/2dsq-z2qf-9s9e-wus4-30ex: Broken pipe
2025-06-11T22:34:07.824Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 278be0d6-7886-40f6-9e5b-3d2196edc0ee
2025-06-11T22:34:07.945Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-11T22:34:07.959Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-11T22:34:08.882Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-11T22:34:08.901Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-11T22:34:10.326Z [ERROR] 🚨 [ERROR] {
  "errorId": "c83deaca-0288-4d97-b30e-293031d28ce9",
  "timestamp": "2025-06-11T22:34:10.326Z",
  "type": "STREAMING_ERROR",
  "message": "Stream is temporarily disabled due to repeated failures. Please check your RTMP settings.",
  "statusCode": 500,
  "streamId": "8a410b2a-fbcb-46a6-b710-9bdd0036be5b",
  "operation": "startStream",
  "context": {
    "streamId": "8a410b2a-fbcb-46a6-b710-9bdd0036be5b",
    "operation": "start"
  }
}
2025-06-11T22:34:10.326Z [ERROR] [StreamingService] Failed to restart stream: Stream is temporarily disabled due to repeated failures. Please check your RTMP settings.
2025-06-11T22:34:10.417Z [ERROR] 🚨 [ERROR] {
  "errorId": "77fa8f2b-c392-4bc4-bce6-64d985349901",
  "timestamp": "2025-06-11T22:34:10.417Z",
  "type": "STREAMING_ERROR",
  "message": "Stream is temporarily disabled due to repeated failures. Please check your RTMP settings.",
  "statusCode": 500,
  "streamId": "8a410b2a-fbcb-46a6-b710-9bdd0036be5b",
  "operation": "startStream",
  "context": {
    "streamId": "8a410b2a-fbcb-46a6-b710-9bdd0036be5b",
    "operation": "start"
  }
}
2025-06-11T22:34:10.417Z [ERROR] [StreamingService] Failed to restart stream: Stream is temporarily disabled due to repeated failures. Please check your RTMP settings.
2025-06-11T22:35:45.947Z [ERROR] [FFMPEG_STDERR] 023f6130-8386-46d1-8a79-0389e2bd5d6f: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/7c02-u19u-2pwj-vc92-3c7a: Connection reset by peer
2025-06-11T22:35:45.964Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 023f6130-8386-46d1-8a79-0389e2bd5d6f
2025-06-11T22:36:00.221Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-11T22:36:00.225Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-11T22:36:00.230Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-11T22:36:00.232Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-11T22:41:46.992Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-11T22:41:47.003Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-11T22:41:48.980Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-11T22:41:48.996Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-11T22:41:50.006Z [ERROR] 🚨 [ERROR] {
  "errorId": "db7e64d8-4f07-4d46-a4a4-b97aa9846977",
  "timestamp": "2025-06-11T22:41:50.006Z",
  "type": "STREAMING_ERROR",
  "message": "Stream is temporarily disabled due to repeated failures. Please check your RTMP settings.",
  "statusCode": 500,
  "streamId": "8a410b2a-fbcb-46a6-b710-9bdd0036be5b",
  "operation": "startStream",
  "context": {
    "streamId": "8a410b2a-fbcb-46a6-b710-9bdd0036be5b",
    "operation": "start"
  }
}
2025-06-11T22:41:50.007Z [ERROR] [StreamingService] Failed to restart stream: Stream is temporarily disabled due to repeated failures. Please check your RTMP settings.
2025-06-11T22:41:51.999Z [ERROR] 🚨 [ERROR] {
  "errorId": "5a30534b-9f97-4a05-a0e6-37e12060d7ab",
  "timestamp": "2025-06-11T22:41:51.999Z",
  "type": "STREAMING_ERROR",
  "message": "Stream is temporarily disabled due to repeated failures. Please check your RTMP settings.",
  "statusCode": 500,
  "streamId": "8a410b2a-fbcb-46a6-b710-9bdd0036be5b",
  "operation": "startStream",
  "context": {
    "streamId": "8a410b2a-fbcb-46a6-b710-9bdd0036be5b",
    "operation": "start"
  }
}
2025-06-11T22:41:51.999Z [ERROR] [StreamingService] Failed to restart stream: Stream is temporarily disabled due to repeated failures. Please check your RTMP settings.
2025-06-11T23:37:13.789Z [ERROR] ⚠️  High memory usage: 90.78%
2025-06-11T23:42:13.790Z [ERROR] ⚠️  High memory usage: 91.73%
2025-06-12T00:57:13.795Z [ERROR] ⚠️  High memory usage: 90.64%
2025-06-12T01:17:13.796Z [ERROR] ⚠️  High memory usage: 91.97%
2025-06-12T02:52:13.797Z [ERROR] ⚠️  High memory usage: 91.52%
2025-06-12T05:57:13.808Z [ERROR] ⚠️  High memory usage: 90.16%
2025-06-12T06:32:13.809Z [ERROR] ⚠️  High memory usage: 90.10%
2025-06-12T06:36:36.472Z [ERROR] [FFMPEG_STDERR] 0c1aa411-c9b7-4826-875f-11aa850d55aa: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/e0q7-kmu5-vtsu-z1rw-0m1z: Connection reset by peer
2025-06-12T06:36:36.498Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 0c1aa411-c9b7-4826-875f-11aa850d55aa
2025-06-12T06:54:04.422Z [ERROR] [FFMPEG_STDERR] f164d7c3-216f-43d5-8945-e8899ffed16e: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/e1wd-xzha-gy6h-phzx-cmzf: Broken pipe
2025-06-12T06:54:04.453Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream f164d7c3-216f-43d5-8945-e8899ffed16e
2025-06-12T07:12:23.044Z [ERROR] [FFMPEG_STDERR] 0c1aa411-c9b7-4826-875f-11aa850d55aa: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/e0q7-kmu5-vtsu-z1rw-0m1z: Broken pipe
2025-06-12T07:12:23.068Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 0c1aa411-c9b7-4826-875f-11aa850d55aa
2025-06-12T07:59:43.989Z [ERROR] [FFMPEG_STDERR] 023f6130-8386-46d1-8a79-0389e2bd5d6f: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/7c02-u19u-2pwj-vc92-3c7a: End of file
2025-06-12T07:59:44.032Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 023f6130-8386-46d1-8a79-0389e2bd5d6f
2025-06-12T08:02:13.819Z [ERROR] ⚠️  High memory usage: 91.70%
2025-06-12T08:12:13.819Z [ERROR] ⚠️  High memory usage: 90.22%
2025-06-12T08:17:00.065Z [ERROR] [FFMPEG_STDERR] 4cf3d51b-cc1a-45e3-ac0a-1d397577a5cd: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/wztd-9a5g-2uxd-1q4r-9mcp: Broken pipe
2025-06-12T08:17:00.081Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 4cf3d51b-cc1a-45e3-ac0a-1d397577a5cd
2025-06-12T08:17:13.819Z [ERROR] ⚠️  High memory usage: 92.18%
2025-06-12T08:27:48.542Z [ERROR] [FFMPEG_STDERR] dd55e4d2-670b-4415-a09d-8d7b5f6bd5c7: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/k47s-4ac7-165d-mvg0-8w8h: Broken pipe
2025-06-12T08:27:48.563Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream dd55e4d2-670b-4415-a09d-8d7b5f6bd5c7
2025-06-12T08:57:13.821Z [ERROR] ⚠️  High memory usage: 91.36%
2025-06-12T09:02:13.822Z [ERROR] ⚠️  High memory usage: 90.10%
2025-06-12T10:07:13.829Z [ERROR] ⚠️  High memory usage: 91.94%
2025-06-12T10:22:13.831Z [ERROR] ⚠️  High memory usage: 90.86%
2025-06-12T13:02:13.845Z [ERROR] ⚠️  High memory usage: 90.49%
2025-06-12T15:17:47.091Z [ERROR] [StreamingService] Failed to save stream history: TypeError: Cannot read properties of undefined (reading 'start_time')
    at saveStreamHistory (/home/<USER>/streamonpod/services/streamingService.js:1541:17)
    at Object.stopStream (/home/<USER>/streamonpod/services/streamingService.js:1164:13)
    at async /home/<USER>/streamonpod/app.js:3996:24
2025-06-12T18:35:31.726Z [ERROR] [FFMPEG_STDERR] dd55e4d2-670b-4415-a09d-8d7b5f6bd5c7: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/k47s-4ac7-165d-mvg0-8w8h: End of file
2025-06-12T18:35:31.752Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream dd55e4d2-670b-4415-a09d-8d7b5f6bd5c7
2025-06-12T18:35:32.826Z [ERROR] [FFMPEG_STDERR] a601092b-c94a-4e5c-91e0-46bbeb45b3ff: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/4vfp-pshz-g9cj-y5wz-bqsp: Broken pipe
2025-06-12T18:35:32.840Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream a601092b-c94a-4e5c-91e0-46bbeb45b3ff
2025-06-12T18:35:37.205Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-12T18:35:37.221Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-12T18:36:04.020Z [ERROR] [FFMPEG_STDERR] 98022af8-7561-4b99-bd77-39462cfa803a: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/jx8r-02mb-vf09-uhge-a5xx: Broken pipe
2025-06-12T18:36:04.041Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 98022af8-7561-4b99-bd77-39462cfa803a
2025-06-12T18:36:07.834Z [ERROR] [FFMPEG_STDERR] 1168581d-fa9b-4701-bdea-c995c9f9de88: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/mptg-p8v2-vc4t-kp2g-3yfc: Broken pipe
2025-06-12T18:36:07.850Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 1168581d-fa9b-4701-bdea-c995c9f9de88
2025-06-13T15:52:13.986Z [ERROR] ⚠️  High memory usage: 91.12%
2025-06-13T17:37:13.995Z [ERROR] ⚠️  High memory usage: 90.39%
2025-06-13T17:42:13.995Z [ERROR] ⚠️  High memory usage: 90.86%
2025-06-14T00:52:14.021Z [ERROR] ⚠️  High memory usage: 90.49%
2025-06-14T01:02:14.023Z [ERROR] ⚠️  High memory usage: 91.19%
2025-06-14T15:17:14.083Z [ERROR] ⚠️  High memory usage: 90.31%
2025-06-15T03:47:14.146Z [ERROR] ⚠️  High memory usage: 93.81%
2025-06-15T03:52:14.146Z [ERROR] ⚠️  High memory usage: 94.58%
2025-06-15T03:57:14.147Z [ERROR] ⚠️  High memory usage: 93.83%
2025-06-15T04:22:14.148Z [ERROR] ⚠️  High memory usage: 90.83%
2025-06-15T04:47:14.150Z [ERROR] ⚠️  High memory usage: 91.27%
2025-06-15T05:07:14.153Z [ERROR] ⚠️  High memory usage: 91.33%
2025-06-15T05:12:14.153Z [ERROR] ⚠️  High memory usage: 93.03%
2025-06-15T05:32:14.155Z [ERROR] ⚠️  High memory usage: 91.99%
2025-06-15T06:17:14.157Z [ERROR] ⚠️  High memory usage: 90.85%
2025-06-15T06:37:14.159Z [ERROR] ⚠️  High memory usage: 90.88%
2025-06-15T07:32:14.166Z [ERROR] ⚠️  High memory usage: 92.13%
2025-06-15T07:57:14.168Z [ERROR] ⚠️  High memory usage: 90.49%
2025-06-15T08:17:14.168Z [ERROR] ⚠️  High memory usage: 91.94%
2025-06-15T09:12:14.177Z [ERROR] ⚠️  High memory usage: 90.63%
2025-06-15T10:32:14.181Z [ERROR] ⚠️  High memory usage: 90.41%
2025-06-15T13:37:14.194Z [ERROR] ⚠️  High memory usage: 90.84%
2025-06-15T13:40:19.927Z [ERROR] [FFMPEG_STDERR] af7608d8-833a-402b-bf8c-33b835bdd69c: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/gw6c-e7tv-s7a2-mu53-4zyy: Broken pipe
2025-06-15T13:40:19.948Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream af7608d8-833a-402b-bf8c-33b835bdd69c
2025-06-15T13:43:23.251Z [ERROR] [FFMPEG_STDERR] af7608d8-833a-402b-bf8c-33b835bdd69c: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/gw6c-e7tv-s7a2-mu53-4zyy: Broken pipe
2025-06-15T13:43:23.260Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream af7608d8-833a-402b-bf8c-33b835bdd69c
2025-06-15T13:48:16.116Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-15T13:48:16.133Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-15T13:52:44.403Z [ERROR] [FFMPEG_STDERR] af7608d8-833a-402b-bf8c-33b835bdd69c: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/gw6c-e7tv-s7a2-mu53-4zyy: Broken pipe
2025-06-15T13:52:44.413Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream af7608d8-833a-402b-bf8c-33b835bdd69c
2025-06-15T14:20:37.876Z [ERROR] [FFMPEG_STDERR] af7608d8-833a-402b-bf8c-33b835bdd69c: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/gw6c-e7tv-s7a2-mu53-4zyy: Broken pipe
2025-06-15T14:20:37.903Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream af7608d8-833a-402b-bf8c-33b835bdd69c
2025-06-15T14:49:24.059Z [ERROR] Error fetching current UTC time: connect ETIMEDOUT **************:443
2025-06-15T15:17:14.201Z [ERROR] ⚠️  High memory usage: 90.20%
2025-06-15T15:42:14.203Z [ERROR] ⚠️  High memory usage: 90.40%
2025-06-15T16:22:14.207Z [ERROR] ⚠️  High memory usage: 91.13%
2025-06-15T16:52:14.207Z [ERROR] ⚠️  High memory usage: 90.36%
2025-06-15T17:42:14.212Z [ERROR] ⚠️  High memory usage: 90.30%
2025-06-15T18:57:14.220Z [ERROR] ⚠️  High memory usage: 90.46%
2025-06-15T19:22:14.222Z [ERROR] ⚠️  High memory usage: 90.77%
2025-06-15T20:17:14.226Z [ERROR] ⚠️  High memory usage: 90.86%
2025-06-15T21:07:14.228Z [ERROR] ⚠️  High memory usage: 91.37%
2025-06-15T21:27:14.228Z [ERROR] ⚠️  High memory usage: 91.12%
2025-06-15T23:32:14.238Z [ERROR] ⚠️  High memory usage: 90.37%
2025-06-16T02:27:14.253Z [ERROR] ⚠️  High memory usage: 90.23%
2025-06-16T03:32:14.264Z [ERROR] ⚠️  High memory usage: 90.96%
2025-06-16T15:27:14.315Z [ERROR] ⚠️  High memory usage: 92.68%
2025-06-16T15:32:14.316Z [ERROR] ⚠️  High memory usage: 93.56%
2025-06-16T17:12:14.324Z [ERROR] ⚠️  High memory usage: 90.19%
2025-06-16T19:17:14.332Z [ERROR] ⚠️  High memory usage: 90.50%
2025-06-16T22:41:35.065Z [ERROR] [FFMPEG_STDERR] dd55e4d2-670b-4415-a09d-8d7b5f6bd5c7: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/k47s-4ac7-165d-mvg0-8w8h: Connection timed out
2025-06-16T22:41:35.102Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream dd55e4d2-670b-4415-a09d-8d7b5f6bd5c7
2025-06-17T02:45:03.172Z [ERROR] [FFMPEG_STDERR] dd55e4d2-670b-4415-a09d-8d7b5f6bd5c7: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/k47s-4ac7-165d-mvg0-8w8h: Connection reset by peer
2025-06-17T02:45:03.226Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream dd55e4d2-670b-4415-a09d-8d7b5f6bd5c7
2025-06-17T12:42:14.417Z [ERROR] ⚠️  High memory usage: 90.75%
2025-06-17T21:54:04.503Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-17T21:54:04.535Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-17T22:55:18.504Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-17T22:55:18.532Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-18T00:04:42.580Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-18T00:04:42.602Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-18T09:17:17.891Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-18T09:17:17.912Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-18T09:22:07.865Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-18T09:22:07.899Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-18T10:19:45.876Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-18T10:19:45.906Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-18T11:31:09.928Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-18T11:31:09.947Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-18T11:31:11.359Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-18T11:31:11.376Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-18T11:31:11.459Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-18T11:31:11.473Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-18T11:31:11.717Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-18T11:31:11.734Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-18T11:31:11.734Z [ERROR] [StreamingService] Maximum retry attempts (3) reached for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-18T11:31:11.950Z [ERROR] [FFMPEG_STDERR] 023f6130-8386-46d1-8a79-0389e2bd5d6f: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/7c02-u19u-2pwj-vc92-3c7a: Broken pipe
2025-06-18T11:31:11.969Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 023f6130-8386-46d1-8a79-0389e2bd5d6f
2025-06-18T11:31:11.981Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-18T11:31:11.996Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-18T11:31:12.001Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-18T11:31:12.016Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-18T11:31:12.061Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-18T11:31:12.065Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-18T11:31:12.067Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-18T11:31:12.069Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-18T11:31:12.087Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-18T11:31:12.101Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-18T11:31:12.102Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-18T11:31:12.102Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-18T11:31:12.477Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-18T11:31:12.495Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-18T11:31:12.950Z [ERROR] 🚨 [ERROR] {
  "errorId": "e8635f7d-a4f0-4d2c-bc72-a659b379a6fa",
  "timestamp": "2025-06-18T11:31:12.949Z",
  "type": "STREAMING_ERROR",
  "message": "Stream is temporarily disabled due to repeated failures. Please check your RTMP settings.",
  "statusCode": 500,
  "streamId": "8a410b2a-fbcb-46a6-b710-9bdd0036be5b",
  "operation": "startStream",
  "context": {
    "streamId": "8a410b2a-fbcb-46a6-b710-9bdd0036be5b",
    "operation": "start"
  }
}
2025-06-18T11:31:12.950Z [ERROR] [StreamingService] Failed to restart stream: Stream is temporarily disabled due to repeated failures. Please check your RTMP settings.
2025-06-18T11:31:14.378Z [ERROR] 🚨 [ERROR] {
  "errorId": "7ed56b0b-6dce-4035-8b85-acdff8821354",
  "timestamp": "2025-06-18T11:31:14.378Z",
  "type": "STREAMING_ERROR",
  "message": "Stream is temporarily disabled due to repeated failures. Please check your RTMP settings.",
  "statusCode": 500,
  "streamId": "8a410b2a-fbcb-46a6-b710-9bdd0036be5b",
  "operation": "startStream",
  "context": {
    "streamId": "8a410b2a-fbcb-46a6-b710-9bdd0036be5b",
    "operation": "start"
  }
}
2025-06-18T11:31:14.378Z [ERROR] [StreamingService] Failed to restart stream: Stream is temporarily disabled due to repeated failures. Please check your RTMP settings.
2025-06-18T11:31:14.475Z [ERROR] 🚨 [ERROR] {
  "errorId": "4cf39ebc-8a12-4e9d-a064-78ee16ac4d83",
  "timestamp": "2025-06-18T11:31:14.475Z",
  "type": "STREAMING_ERROR",
  "message": "Stream is temporarily disabled due to repeated failures. Please check your RTMP settings.",
  "statusCode": 500,
  "streamId": "8a410b2a-fbcb-46a6-b710-9bdd0036be5b",
  "operation": "startStream",
  "context": {
    "streamId": "8a410b2a-fbcb-46a6-b710-9bdd0036be5b",
    "operation": "start"
  }
}
2025-06-18T11:31:14.475Z [ERROR] [StreamingService] Failed to restart stream: Stream is temporarily disabled due to repeated failures. Please check your RTMP settings.
2025-06-18T11:31:14.567Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-18T11:31:14.580Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-18T12:32:23.857Z [ERROR] [FFMPEG_STDERR] 5b81ecbb-1797-4aaf-b5a4-5563cc476b3a: av_interleaved_write_frame(): Broken pipe
Error writing trailer of rtmp://a.rtmp.youtube.com/live2/murk-epb3-1gjj-v5df-9h70: Broken pipe
2025-06-18T12:32:23.887Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 5b81ecbb-1797-4aaf-b5a4-5563cc476b3a
2025-06-18T13:22:09.888Z [ERROR] [FFMPEG_STDERR] 5b81ecbb-1797-4aaf-b5a4-5563cc476b3a: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/murk-epb3-1gjj-v5df-9h70: Broken pipe
2025-06-18T13:22:09.908Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 5b81ecbb-1797-4aaf-b5a4-5563cc476b3a
2025-06-18T13:22:30.901Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-18T13:22:30.917Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-18T13:22:37.024Z [ERROR] [FFMPEG_STDERR] 023f6130-8386-46d1-8a79-0389e2bd5d6f: av_interleaved_write_frame(): Broken pipe
Error writing trailer of rtmp://a.rtmp.youtube.com/live2/7c02-u19u-2pwj-vc92-3c7a: Broken pipe
2025-06-18T13:22:37.042Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 023f6130-8386-46d1-8a79-0389e2bd5d6f
2025-06-18T13:45:16.946Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-18T13:45:16.970Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-18T13:45:17.525Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-18T13:45:17.546Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-18T13:45:18.187Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-18T13:45:18.205Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-18T13:45:18.484Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-18T13:45:18.500Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-18T13:45:18.500Z [ERROR] [StreamingService] Maximum retry attempts (3) reached for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-18T13:45:19.972Z [ERROR] 🚨 [ERROR] {
  "errorId": "222c3446-96d5-44e9-ba6d-c192546db290",
  "timestamp": "2025-06-18T13:45:19.972Z",
  "type": "STREAMING_ERROR",
  "message": "Stream is temporarily disabled due to repeated failures. Please check your RTMP settings.",
  "statusCode": 500,
  "streamId": "8a410b2a-fbcb-46a6-b710-9bdd0036be5b",
  "operation": "startStream",
  "context": {
    "streamId": "8a410b2a-fbcb-46a6-b710-9bdd0036be5b",
    "operation": "start"
  }
}
2025-06-18T13:45:19.972Z [ERROR] [StreamingService] Failed to restart stream: Stream is temporarily disabled due to repeated failures. Please check your RTMP settings.
2025-06-18T13:45:20.548Z [ERROR] 🚨 [ERROR] {
  "errorId": "78cf0824-573e-498c-b05c-3eea5e8a44aa",
  "timestamp": "2025-06-18T13:45:20.548Z",
  "type": "STREAMING_ERROR",
  "message": "Stream is temporarily disabled due to repeated failures. Please check your RTMP settings.",
  "statusCode": 500,
  "streamId": "8a410b2a-fbcb-46a6-b710-9bdd0036be5b",
  "operation": "startStream",
  "context": {
    "streamId": "8a410b2a-fbcb-46a6-b710-9bdd0036be5b",
    "operation": "start"
  }
}
2025-06-18T13:45:20.548Z [ERROR] [StreamingService] Failed to restart stream: Stream is temporarily disabled due to repeated failures. Please check your RTMP settings.
2025-06-18T13:45:21.208Z [ERROR] 🚨 [ERROR] {
  "errorId": "a51513f5-79c9-494b-9ec6-992236f08058",
  "timestamp": "2025-06-18T13:45:21.208Z",
  "type": "STREAMING_ERROR",
  "message": "Stream is temporarily disabled due to repeated failures. Please check your RTMP settings.",
  "statusCode": 500,
  "streamId": "8a410b2a-fbcb-46a6-b710-9bdd0036be5b",
  "operation": "startStream",
  "context": {
    "streamId": "8a410b2a-fbcb-46a6-b710-9bdd0036be5b",
    "operation": "start"
  }
}
2025-06-18T13:45:21.208Z [ERROR] [StreamingService] Failed to restart stream: Stream is temporarily disabled due to repeated failures. Please check your RTMP settings.
2025-06-18T18:18:47.423Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-18T18:18:47.443Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-19T01:02:14.592Z [ERROR] ⚠️  High memory usage: 91.15%
2025-06-19T01:22:14.594Z [ERROR] ⚠️  High memory usage: 90.54%
2025-06-19T05:32:14.615Z [ERROR] ⚠️  High memory usage: 90.39%
2025-06-19T05:37:14.615Z [ERROR] ⚠️  High memory usage: 90.70%
2025-06-19T05:47:14.615Z [ERROR] ⚠️  High memory usage: 92.97%
2025-06-19T05:52:14.616Z [ERROR] ⚠️  High memory usage: 92.77%
2025-06-19T06:07:14.618Z [ERROR] ⚠️  High memory usage: 93.35%
2025-06-19T06:32:14.621Z [ERROR] ⚠️  High memory usage: 92.26%
2025-06-19T07:07:14.622Z [ERROR] ⚠️  High memory usage: 90.05%
2025-06-19T07:32:14.624Z [ERROR] ⚠️  High memory usage: 91.50%
2025-06-19T10:01:15.112Z [ERROR] [FFMPEG_STDERR] 023f6130-8386-46d1-8a79-0389e2bd5d6f: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/7c02-u19u-2pwj-vc92-3c7a: Broken pipe
2025-06-19T10:01:15.143Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 023f6130-8386-46d1-8a79-0389e2bd5d6f
2025-06-19T10:45:35.666Z [ERROR] [FFMPEG_STDERR] 023f6130-8386-46d1-8a79-0389e2bd5d6f: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/7c02-u19u-2pwj-vc92-3c7a: Connection reset by peer
2025-06-19T10:45:35.689Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 023f6130-8386-46d1-8a79-0389e2bd5d6f
2025-06-19T10:59:45.379Z [ERROR] [FFMPEG_STDERR] 1ed7db3a-1601-4f44-8fd9-6094bba87a43: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/4vfp-pshz-g9cj-y5wz-bqsp: Broken pipe
2025-06-19T10:59:45.395Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 1ed7db3a-1601-4f44-8fd9-6094bba87a43
2025-06-19T11:10:22.138Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Connection reset by peer
2025-06-19T11:10:22.155Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-19T11:24:41.688Z [ERROR] [FFMPEG_STDERR] 1ed7db3a-1601-4f44-8fd9-6094bba87a43: av_interleaved_write_frame(): Broken pipe
Error writing trailer of rtmp://a.rtmp.youtube.com/live2/4vfp-pshz-g9cj-y5wz-bqsp: Broken pipe
2025-06-19T11:24:41.710Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 1ed7db3a-1601-4f44-8fd9-6094bba87a43
2025-06-19T14:02:14.648Z [ERROR] ⚠️  High memory usage: 93.43%
2025-06-19T14:07:14.649Z [ERROR] ⚠️  High memory usage: 92.75%
2025-06-19T14:12:14.649Z [ERROR] ⚠️  High memory usage: 94.06%
2025-06-19T14:17:14.651Z [ERROR] ⚠️  High memory usage: 92.63%
2025-06-19T14:22:14.651Z [ERROR] ⚠️  High memory usage: 94.39%
2025-06-19T14:27:14.652Z [ERROR] ⚠️  High memory usage: 91.94%
2025-06-19T14:32:14.652Z [ERROR] ⚠️  High memory usage: 91.65%
2025-06-19T14:37:14.652Z [ERROR] ⚠️  High memory usage: 93.40%
2025-06-19T14:42:14.653Z [ERROR] ⚠️  High memory usage: 92.43%
2025-06-19T14:47:14.654Z [ERROR] ⚠️  High memory usage: 92.28%
2025-06-19T14:52:14.654Z [ERROR] ⚠️  High memory usage: 93.47%
2025-06-19T15:12:14.654Z [ERROR] ⚠️  High memory usage: 90.97%
2025-06-19T15:17:14.655Z [ERROR] ⚠️  High memory usage: 91.19%
2025-06-19T15:22:14.655Z [ERROR] ⚠️  High memory usage: 93.26%
2025-06-19T15:47:14.658Z [ERROR] ⚠️  High memory usage: 90.89%
2025-06-19T16:07:14.659Z [ERROR] ⚠️  High memory usage: 90.53%
2025-06-19T16:12:14.659Z [ERROR] ⚠️  High memory usage: 92.06%
2025-06-19T16:37:14.661Z [ERROR] ⚠️  High memory usage: 91.73%
2025-06-19T16:42:14.662Z [ERROR] ⚠️  High memory usage: 93.40%
2025-06-19T17:07:14.665Z [ERROR] ⚠️  High memory usage: 91.98%
2025-06-19T17:32:14.667Z [ERROR] ⚠️  High memory usage: 91.25%
2025-06-19T17:37:14.667Z [ERROR] ⚠️  High memory usage: 92.71%
2025-06-19T17:57:14.670Z [ERROR] ⚠️  High memory usage: 90.21%
2025-06-19T18:27:14.673Z [ERROR] ⚠️  High memory usage: 90.19%
2025-06-19T18:44:07.483Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-19T18:44:07.500Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-19T18:44:08.225Z [ERROR] [FFMPEG_STDERR] 1ed7db3a-1601-4f44-8fd9-6094bba87a43: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/4vfp-pshz-g9cj-y5wz-bqsp: Broken pipe
2025-06-19T18:44:08.243Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 1ed7db3a-1601-4f44-8fd9-6094bba87a43
2025-06-19T18:52:14.674Z [ERROR] ⚠️  High memory usage: 90.65%
2025-06-19T19:42:14.682Z [ERROR] ⚠️  High memory usage: 91.92%
2025-06-19T20:37:14.690Z [ERROR] ⚠️  High memory usage: 90.79%
2025-06-19T21:52:14.694Z [ERROR] ⚠️  High memory usage: 91.27%
2025-06-20T00:07:14.703Z [ERROR] ⚠️  High memory usage: 90.62%
2025-06-20T00:42:14.707Z [ERROR] ⚠️  High memory usage: 91.86%
2025-06-20T00:52:14.708Z [ERROR] ⚠️  High memory usage: 90.38%
2025-06-20T00:57:14.709Z [ERROR] ⚠️  High memory usage: 91.20%
2025-06-20T01:02:14.709Z [ERROR] ⚠️  High memory usage: 92.66%
2025-06-20T01:07:14.710Z [ERROR] ⚠️  High memory usage: 92.44%
2025-06-20T01:27:14.711Z [ERROR] ⚠️  High memory usage: 91.65%
2025-06-20T02:17:14.717Z [ERROR] ⚠️  High memory usage: 90.33%
2025-06-20T02:42:14.718Z [ERROR] ⚠️  High memory usage: 91.67%
2025-06-20T02:52:14.718Z [ERROR] ⚠️  High memory usage: 90.25%
2025-06-20T04:42:14.730Z [ERROR] ⚠️  High memory usage: 90.29%
2025-06-20T05:02:14.732Z [ERROR] ⚠️  High memory usage: 90.34%
2025-06-20T05:47:14.736Z [ERROR] ⚠️  High memory usage: 90.35%
2025-06-20T08:17:14.746Z [ERROR] ⚠️  High memory usage: 91.16%
2025-06-20T08:22:14.747Z [ERROR] ⚠️  High memory usage: 92.28%
2025-06-20T08:27:14.747Z [ERROR] ⚠️  High memory usage: 92.76%
2025-06-20T08:32:14.747Z [ERROR] ⚠️  High memory usage: 91.90%
2025-06-20T08:37:14.747Z [ERROR] ⚠️  High memory usage: 92.36%
2025-06-20T08:57:14.749Z [ERROR] ⚠️  High memory usage: 90.25%
2025-06-20T09:02:14.750Z [ERROR] ⚠️  High memory usage: 91.83%
2025-06-20T09:07:14.750Z [ERROR] ⚠️  High memory usage: 91.71%
2025-06-20T09:27:14.751Z [ERROR] ⚠️  High memory usage: 91.37%
2025-06-20T09:32:14.750Z [ERROR] ⚠️  High memory usage: 91.80%
2025-06-20T09:57:14.753Z [ERROR] ⚠️  High memory usage: 91.25%
2025-06-20T11:47:14.765Z [ERROR] ⚠️  High memory usage: 92.65%
2025-06-20T12:37:14.768Z [ERROR] ⚠️  High memory usage: 91.03%
2025-06-20T13:02:14.772Z [ERROR] ⚠️  High memory usage: 90.99%
2025-06-20T13:32:14.783Z [ERROR] ⚠️  High memory usage: 90.53%
2025-06-20T13:52:14.785Z [ERROR] ⚠️  High memory usage: 90.70%
2025-06-20T14:12:34.202Z [ERROR] [StreamingService] Failed to save stream history: TypeError: Cannot read properties of undefined (reading 'start_time')
    at saveStreamHistory (/home/<USER>/streamonpod/services/streamingService.js:1541:17)
    at Object.stopStream (/home/<USER>/streamonpod/services/streamingService.js:1164:13)
    at async /home/<USER>/streamonpod/app.js:3996:24
2025-06-20T14:22:14.786Z [ERROR] ⚠️  High memory usage: 92.01%
2025-06-20T14:37:14.787Z [ERROR] ⚠️  High memory usage: 92.51%
2025-06-20T15:02:14.788Z [ERROR] ⚠️  High memory usage: 92.37%
2025-06-20T15:12:14.789Z [ERROR] ⚠️  High memory usage: 92.69%
2025-06-20T15:17:14.789Z [ERROR] ⚠️  High memory usage: 90.03%
2025-06-20T20:07:03.680Z [ERROR] [FFMPEG_STDERR] 023f6130-8386-46d1-8a79-0389e2bd5d6f: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/7c02-u19u-2pwj-vc92-3c7a: Connection reset by peer
2025-06-20T20:07:03.698Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 023f6130-8386-46d1-8a79-0389e2bd5d6f
2025-06-20T20:07:03.791Z [ERROR] [FFMPEG_STDERR] e52e21cf-875c-4a82-9566-413538629dd1: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/4vfp-pshz-g9cj-y5wz-bqsp: Connection reset by peer
2025-06-20T20:07:03.805Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream e52e21cf-875c-4a82-9566-413538629dd1
2025-06-20T20:17:29.846Z [ERROR] Error fetching current UTC time: getaddrinfo EAI_AGAIN timeapi.io
2025-06-21T01:12:14.836Z [ERROR] ⚠️  High memory usage: 92.20%
2025-06-21T02:56:30.573Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-21T02:56:30.596Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-21T16:40:41.762Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-21T16:40:41.782Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-22T06:07:14.973Z [ERROR] ⚠️  High memory usage: 90.35%
2025-06-22T06:12:14.973Z [ERROR] ⚠️  High memory usage: 91.21%
2025-06-22T06:22:14.974Z [ERROR] ⚠️  High memory usage: 90.37%
2025-06-22T08:17:14.983Z [ERROR] ⚠️  High memory usage: 90.84%
2025-06-22T12:57:15.006Z [ERROR] ⚠️  High memory usage: 90.44%
2025-06-22T13:02:15.006Z [ERROR] ⚠️  High memory usage: 90.27%
2025-06-22T15:47:36.491Z [ERROR] Error fetching current UTC time: Client network socket disconnected before secure TLS connection was established
2025-06-22T15:47:44.855Z [ERROR] Error fetching current UTC time: Client network socket disconnected before secure TLS connection was established
2025-06-23T04:02:31.750Z [ERROR] [FFMPEG_STDERR] 023f6130-8386-46d1-8a79-0389e2bd5d6f: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/7c02-u19u-2pwj-vc92-3c7a: Broken pipe
2025-06-23T04:02:31.769Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 023f6130-8386-46d1-8a79-0389e2bd5d6f
2025-06-23T08:27:15.115Z [ERROR] ⚠️  High memory usage: 92.90%
2025-06-23T08:32:15.116Z [ERROR] ⚠️  High memory usage: 90.88%
2025-06-23T08:37:15.117Z [ERROR] ⚠️  High memory usage: 93.66%
2025-06-23T08:57:15.119Z [ERROR] ⚠️  High memory usage: 90.03%
2025-06-23T09:02:15.119Z [ERROR] ⚠️  High memory usage: 90.95%
2025-06-23T09:27:15.121Z [ERROR] ⚠️  High memory usage: 90.94%
2025-06-23T09:57:15.123Z [ERROR] ⚠️  High memory usage: 92.00%
2025-06-23T10:22:15.124Z [ERROR] ⚠️  High memory usage: 90.18%
2025-06-23T10:52:15.125Z [ERROR] ⚠️  High memory usage: 92.81%
2025-06-23T12:02:15.130Z [ERROR] ⚠️  High memory usage: 90.74%
2025-06-23T14:17:15.146Z [ERROR] ⚠️  High memory usage: 90.73%
2025-06-23T15:27:15.159Z [ERROR] ⚠️  High memory usage: 90.62%
2025-06-24T02:15:21.823Z [ERROR] ❌ Failed to process chunk 5: Error: Chunk 5 already received
    at ChunkedUploadService.processChunk (/home/<USER>/streamonpod/utils/chunkedUploadService.js:115:15)
    at async /home/<USER>/streamonpod/app.js:2407:20
2025-06-24T02:15:21.828Z [ERROR] ❌ Error processing chunk: Error: Failed to process chunk: Chunk 5 already received
    at ChunkedUploadService.processChunk (/home/<USER>/streamonpod/utils/chunkedUploadService.js:156:13)
    at async /home/<USER>/streamonpod/app.js:2407:20
2025-06-24T02:15:34.236Z [ERROR] ❌ Failed to process chunk 5: Error: Chunk 5 already received
    at ChunkedUploadService.processChunk (/home/<USER>/streamonpod/utils/chunkedUploadService.js:115:15)
    at async /home/<USER>/streamonpod/app.js:2407:20
2025-06-24T02:15:34.237Z [ERROR] ❌ Error processing chunk: Error: Failed to process chunk: Chunk 5 already received
    at ChunkedUploadService.processChunk (/home/<USER>/streamonpod/utils/chunkedUploadService.js:156:13)
    at async /home/<USER>/streamonpod/app.js:2407:20
2025-06-24T02:32:15.211Z [ERROR] ⚠️  High memory usage: 90.77%
2025-06-24T02:37:15.211Z [ERROR] ⚠️  High memory usage: 92.71%
2025-06-24T02:47:15.211Z [ERROR] ⚠️  High memory usage: 91.85%
2025-06-24T03:02:15.211Z [ERROR] ⚠️  High memory usage: 91.39%
2025-06-24T04:22:15.225Z [ERROR] ⚠️  High memory usage: 90.23%
2025-06-24T04:57:15.229Z [ERROR] ⚠️  High memory usage: 91.99%
2025-06-24T05:02:15.229Z [ERROR] ⚠️  High memory usage: 91.38%
2025-06-24T05:17:15.230Z [ERROR] ⚠️  High memory usage: 92.52%
2025-06-24T05:27:15.230Z [ERROR] ⚠️  High memory usage: 90.38%
2025-06-24T06:37:15.231Z [ERROR] ⚠️  High memory usage: 92.16%
2025-06-24T07:07:15.234Z [ERROR] ⚠️  High memory usage: 91.74%
2025-06-24T07:32:15.236Z [ERROR] ⚠️  High memory usage: 90.17%
2025-06-24T15:27:15.270Z [ERROR] ⚠️  High memory usage: 90.05%
2025-06-25T02:37:15.316Z [ERROR] ⚠️  High memory usage: 90.22%
2025-06-25T02:47:15.316Z [ERROR] ⚠️  High memory usage: 90.00%
2025-06-25T02:52:15.316Z [ERROR] ⚠️  High memory usage: 91.45%
2025-06-25T02:57:15.317Z [ERROR] ⚠️  High memory usage: 91.46%
2025-06-25T03:07:15.318Z [ERROR] ⚠️  High memory usage: 91.19%
2025-06-25T03:32:15.322Z [ERROR] ⚠️  High memory usage: 90.54%
2025-06-25T06:52:15.341Z [ERROR] ⚠️  High memory usage: 91.03%
2025-06-25T06:57:15.347Z [ERROR] ⚠️  High memory usage: 90.75%
2025-06-25T07:07:15.357Z [ERROR] ⚠️  High memory usage: 90.88%
2025-06-25T07:17:15.357Z [ERROR] ⚠️  High memory usage: 90.97%
2025-06-25T07:59:00.472Z [ERROR] [FFMPEG_STDERR] faaa9c6c-85f1-4489-9f01-477c3d028c33: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/4vfp-pshz-g9cj-y5wz-bqsp: Broken pipe
2025-06-25T07:59:00.501Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream faaa9c6c-85f1-4489-9f01-477c3d028c33
2025-06-25T09:22:15.365Z [ERROR] ⚠️  High memory usage: 90.17%
2025-06-25T09:37:15.366Z [ERROR] ⚠️  High memory usage: 90.13%
2025-06-25T14:57:50.944Z [ERROR] [FFMPEG_STDERR] 023f6130-8386-46d1-8a79-0389e2bd5d6f: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/7c02-u19u-2pwj-vc92-3c7a: Broken pipe
2025-06-25T14:57:50.963Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 023f6130-8386-46d1-8a79-0389e2bd5d6f
2025-06-25T15:29:29.221Z [ERROR] [FFMPEG_STDERR] 023f6130-8386-46d1-8a79-0389e2bd5d6f: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/7c02-u19u-2pwj-vc92-3c7a: Broken pipe
2025-06-25T15:29:29.244Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 023f6130-8386-46d1-8a79-0389e2bd5d6f
2025-06-25T17:07:13.887Z [ERROR] [FFMPEG_STDERR] ba5857c7-1ea6-4464-b60a-440ba8b877fc: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/murk-epb3-1gjj-v5df-9h70: Connection reset by peer
2025-06-25T17:07:13.913Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream ba5857c7-1ea6-4464-b60a-440ba8b877fc
2025-06-25T17:07:18.623Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: End of file
2025-06-25T17:07:18.642Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-25T17:07:19.565Z [ERROR] [FFMPEG_STDERR] faaa9c6c-85f1-4489-9f01-477c3d028c33: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/4vfp-pshz-g9cj-y5wz-bqsp: Broken pipe
2025-06-25T17:07:19.593Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream faaa9c6c-85f1-4489-9f01-477c3d028c33
2025-06-26T12:23:39.017Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-26T12:23:39.037Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-26T12:35:32.437Z [ERROR] [FFMPEG_STDERR] ba5857c7-1ea6-4464-b60a-440ba8b877fc: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/murk-epb3-1gjj-v5df-9h70: Broken pipe
2025-06-26T12:35:32.457Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream ba5857c7-1ea6-4464-b60a-440ba8b877fc
2025-06-26T12:39:08.252Z [ERROR] [FFMPEG_STDERR] ba5857c7-1ea6-4464-b60a-440ba8b877fc: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/murk-epb3-1gjj-v5df-9h70: Broken pipe
2025-06-26T12:39:08.266Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream ba5857c7-1ea6-4464-b60a-440ba8b877fc
2025-06-26T12:46:54.361Z [ERROR] [FFMPEG_STDERR] 023f6130-8386-46d1-8a79-0389e2bd5d6f: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/7c02-u19u-2pwj-vc92-3c7a: Connection reset by peer
2025-06-26T12:46:54.377Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 023f6130-8386-46d1-8a79-0389e2bd5d6f
2025-06-26T12:48:31.000Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-26T12:48:31.011Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-26T12:59:54.381Z [ERROR] [FFMPEG_STDERR] 023f6130-8386-46d1-8a79-0389e2bd5d6f: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/7c02-u19u-2pwj-vc92-3c7a: Broken pipe
2025-06-26T12:59:54.403Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 023f6130-8386-46d1-8a79-0389e2bd5d6f
2025-06-26T13:40:08.483Z [ERROR] [FFMPEG_STDERR] ba5857c7-1ea6-4464-b60a-440ba8b877fc: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/murk-epb3-1gjj-v5df-9h70: Broken pipe
2025-06-26T13:40:08.525Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream ba5857c7-1ea6-4464-b60a-440ba8b877fc
2025-06-26T13:42:26.370Z [ERROR] [FFMPEG_STDERR] 023f6130-8386-46d1-8a79-0389e2bd5d6f: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/7c02-u19u-2pwj-vc92-3c7a: Connection reset by peer
2025-06-26T13:42:26.391Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 023f6130-8386-46d1-8a79-0389e2bd5d6f
2025-06-26T13:45:02.547Z [ERROR] [FFMPEG_STDERR] 023f6130-8386-46d1-8a79-0389e2bd5d6f: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/7c02-u19u-2pwj-vc92-3c7a: Broken pipe
2025-06-26T13:45:02.594Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 023f6130-8386-46d1-8a79-0389e2bd5d6f
2025-06-26T17:51:12.595Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-26T17:51:12.621Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-26T17:51:12.825Z [ERROR] [FFMPEG_STDERR] ba5857c7-1ea6-4464-b60a-440ba8b877fc: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/murk-epb3-1gjj-v5df-9h70: Broken pipe
2025-06-26T17:51:12.841Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream ba5857c7-1ea6-4464-b60a-440ba8b877fc
2025-06-26T20:02:48.691Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-26T20:02:48.713Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-27T01:52:15.546Z [ERROR] ⚠️  High memory usage: 91.37%
2025-06-27T02:07:15.547Z [ERROR] ⚠️  High memory usage: 90.55%
2025-06-27T02:32:15.549Z [ERROR] ⚠️  High memory usage: 90.12%
2025-06-27T05:07:15.560Z [ERROR] ⚠️  High memory usage: 90.56%
2025-06-27T09:09:36.391Z [ERROR] [FFMPEG_STDERR] ba5857c7-1ea6-4464-b60a-440ba8b877fc: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/murk-epb3-1gjj-v5df-9h70: Broken pipe
2025-06-27T09:09:36.417Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream ba5857c7-1ea6-4464-b60a-440ba8b877fc
2025-06-28T06:37:15.683Z [ERROR] ⚠️  High memory usage: 92.67%
2025-06-28T06:42:15.684Z [ERROR] ⚠️  High memory usage: 92.09%
2025-06-28T11:07:15.708Z [ERROR] ⚠️  High memory usage: 92.37%
2025-06-28T11:27:15.709Z [ERROR] ⚠️  High memory usage: 90.88%
2025-06-28T11:52:15.711Z [ERROR] ⚠️  High memory usage: 90.01%
2025-06-28T13:47:15.722Z [ERROR] ⚠️  High memory usage: 92.85%
2025-06-28T13:52:15.722Z [ERROR] ⚠️  High memory usage: 93.41%
2025-06-28T14:02:15.723Z [ERROR] ⚠️  High memory usage: 91.03%
2025-06-28T14:17:15.723Z [ERROR] ⚠️  High memory usage: 92.52%
2025-06-28T14:27:15.725Z [ERROR] ⚠️  High memory usage: 91.36%
2025-06-28T16:47:50.232Z [ERROR] Error fetching current UTC time: Client network socket disconnected before secure TLS connection was established
2025-06-28T17:22:15.739Z [ERROR] ⚠️  High memory usage: 90.61%
2025-06-28T17:27:15.756Z [ERROR] ⚠️  High memory usage: 92.21%
2025-06-28T17:32:15.755Z [ERROR] ⚠️  High memory usage: 92.53%
2025-06-28T19:37:15.766Z [ERROR] ⚠️  High memory usage: 90.54%
2025-06-29T06:37:15.832Z [ERROR] ⚠️  High memory usage: 91.48%
2025-06-29T12:37:15.852Z [ERROR] ⚠️  High memory usage: 91.50%
2025-06-29T15:07:15.858Z [ERROR] ⚠️  High memory usage: 90.33%
2025-06-29T15:12:15.859Z [ERROR] ⚠️  High memory usage: 90.31%
2025-06-29T15:17:15.859Z [ERROR] ⚠️  High memory usage: 90.71%
2025-06-29T15:47:15.861Z [ERROR] ⚠️  High memory usage: 91.79%
2025-06-29T16:12:15.862Z [ERROR] ⚠️  High memory usage: 90.31%
2025-06-29T17:12:15.870Z [ERROR] ⚠️  High memory usage: 90.93%
2025-06-29T18:07:15.874Z [ERROR] ⚠️  High memory usage: 90.32%
2025-06-29T18:32:15.875Z [ERROR] ⚠️  High memory usage: 90.25%
2025-06-29T19:02:15.875Z [ERROR] ⚠️  High memory usage: 91.01%
2025-06-29T20:52:15.882Z [ERROR] ⚠️  High memory usage: 91.19%
2025-06-29T21:47:15.885Z [ERROR] ⚠️  High memory usage: 90.00%
2025-06-29T22:47:15.887Z [ERROR] ⚠️  High memory usage: 95.01%
2025-06-29T22:52:15.887Z [ERROR] ⚠️  High memory usage: 90.38%
2025-06-29T22:57:15.888Z [ERROR] ⚠️  High memory usage: 94.86%
2025-06-29T23:02:15.888Z [ERROR] ⚠️  High memory usage: 90.23%
2025-06-29T23:07:15.888Z [ERROR] ⚠️  High memory usage: 93.06%
2025-06-29T23:17:15.890Z [ERROR] ⚠️  High memory usage: 93.98%
2025-06-29T23:22:15.890Z [ERROR] ⚠️  High memory usage: 90.34%
2025-06-29T23:27:15.891Z [ERROR] ⚠️  High memory usage: 94.10%
2025-06-29T23:32:15.892Z [ERROR] ⚠️  High memory usage: 90.47%
2025-06-29T23:42:15.894Z [ERROR] ⚠️  High memory usage: 93.72%
2025-06-29T23:47:15.899Z [ERROR] ⚠️  High memory usage: 91.59%
2025-06-29T23:57:15.895Z [ERROR] ⚠️  High memory usage: 92.53%
2025-06-30T00:07:15.897Z [ERROR] ⚠️  High memory usage: 94.40%
2025-06-30T00:22:15.899Z [ERROR] ⚠️  High memory usage: 91.04%
2025-06-30T00:27:15.899Z [ERROR] ⚠️  High memory usage: 92.72%
2025-06-30T00:32:15.900Z [ERROR] ⚠️  High memory usage: 93.41%
2025-06-30T00:37:15.902Z [ERROR] ⚠️  High memory usage: 94.61%
2025-06-30T00:42:15.903Z [ERROR] ⚠️  High memory usage: 95.21%
2025-06-30T00:47:15.902Z [ERROR] ⚠️  High memory usage: 90.29%
2025-06-30T00:52:15.902Z [ERROR] ⚠️  High memory usage: 90.57%
2025-06-30T00:57:15.903Z [ERROR] ⚠️  High memory usage: 92.68%
2025-06-30T01:07:15.904Z [ERROR] ⚠️  High memory usage: 92.05%
2025-06-30T01:12:15.905Z [ERROR] ⚠️  High memory usage: 93.82%
2025-06-30T01:22:15.906Z [ERROR] ⚠️  High memory usage: 92.41%
2025-06-30T01:27:15.907Z [ERROR] ⚠️  High memory usage: 95.35%
2025-06-30T01:32:15.907Z [ERROR] ⚠️  High memory usage: 92.17%
2025-06-30T01:37:15.908Z [ERROR] ⚠️  High memory usage: 94.69%
2025-06-30T01:42:15.909Z [ERROR] ⚠️  High memory usage: 94.62%
2025-06-30T01:52:15.909Z [ERROR] ⚠️  High memory usage: 91.83%
2025-06-30T02:07:15.911Z [ERROR] ⚠️  High memory usage: 90.17%
2025-06-30T02:17:15.911Z [ERROR] ⚠️  High memory usage: 93.91%
2025-06-30T02:22:15.912Z [ERROR] ⚠️  High memory usage: 92.05%
2025-06-30T02:27:15.913Z [ERROR] ⚠️  High memory usage: 90.58%
2025-06-30T02:42:15.914Z [ERROR] ⚠️  High memory usage: 91.46%
2025-06-30T02:52:15.914Z [ERROR] ⚠️  High memory usage: 93.28%
2025-06-30T03:02:15.915Z [ERROR] ⚠️  High memory usage: 91.44%
2025-06-30T03:17:15.916Z [ERROR] ⚠️  High memory usage: 92.17%
2025-06-30T03:27:15.917Z [ERROR] ⚠️  High memory usage: 91.17%
2025-06-30T03:42:15.917Z [ERROR] ⚠️  High memory usage: 92.14%
2025-06-30T03:52:15.917Z [ERROR] ⚠️  High memory usage: 91.92%
2025-06-30T03:57:15.918Z [ERROR] ⚠️  High memory usage: 92.78%
2025-06-30T04:07:15.918Z [ERROR] ⚠️  High memory usage: 90.67%
2025-06-30T04:27:15.921Z [ERROR] ⚠️  High memory usage: 91.05%
2025-06-30T04:32:15.921Z [ERROR] ⚠️  High memory usage: 91.41%
2025-06-30T04:37:15.921Z [ERROR] ⚠️  High memory usage: 92.14%
2025-06-30T05:02:15.923Z [ERROR] ⚠️  High memory usage: 90.38%
2025-06-30T05:09:12.927Z [ERROR] [FFMPEG_STDERR] a27559ee-23c4-4df3-b67f-b0c36dc0baff: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/4vfp-pshz-g9cj-y5wz-bqsp: Broken pipe
2025-06-30T05:09:12.951Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream a27559ee-23c4-4df3-b67f-b0c36dc0baff
2025-06-30T05:27:15.928Z [ERROR] ⚠️  High memory usage: 90.46%
2025-06-30T05:32:15.929Z [ERROR] ⚠️  High memory usage: 90.72%
2025-06-30T06:02:15.931Z [ERROR] ⚠️  High memory usage: 90.94%
2025-06-30T06:27:15.931Z [ERROR] ⚠️  High memory usage: 90.77%
2025-06-30T06:28:52.447Z [ERROR] [FFMPEG_STDERR] 28b8c843-fc78-4a81-9868-f7fe22d0ef55: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/b4w4-hqbw-amy5-8res-0tfx: Broken pipe
2025-06-30T06:28:52.469Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 28b8c843-fc78-4a81-9868-f7fe22d0ef55
2025-06-30T06:28:53.726Z [ERROR] [FFMPEG_STDERR] 8b70abdf-53a4-45c5-a6fd-80530205782c: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/7qkm-e8h9-cd4x-sq11-65gs: Broken pipe
2025-06-30T06:28:53.743Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8b70abdf-53a4-45c5-a6fd-80530205782c
2025-06-30T06:28:53.983Z [ERROR] [FFMPEG_STDERR] ba5857c7-1ea6-4464-b60a-440ba8b877fc: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/murk-epb3-1gjj-v5df-9h70: Broken pipe
2025-06-30T06:28:54.001Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream ba5857c7-1ea6-4464-b60a-440ba8b877fc
2025-06-30T06:52:15.936Z [ERROR] ⚠️  High memory usage: 90.81%
2025-06-30T06:55:38.898Z [ERROR] [FFMPEG_STDERR] ba5857c7-1ea6-4464-b60a-440ba8b877fc: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/murk-epb3-1gjj-v5df-9h70: Broken pipe
2025-06-30T06:55:38.924Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream ba5857c7-1ea6-4464-b60a-440ba8b877fc
2025-06-30T06:57:15.936Z [ERROR] ⚠️  High memory usage: 91.18%
2025-06-30T07:27:15.940Z [ERROR] ⚠️  High memory usage: 91.31%
2025-06-30T07:52:15.943Z [ERROR] ⚠️  High memory usage: 90.73%
2025-06-30T08:02:15.942Z [ERROR] ⚠️  High memory usage: 91.10%
2025-06-30T08:17:15.943Z [ERROR] ⚠️  High memory usage: 90.34%
2025-06-30T08:22:15.943Z [ERROR] ⚠️  High memory usage: 91.99%
2025-06-30T08:52:15.945Z [ERROR] ⚠️  High memory usage: 91.16%
2025-06-30T09:12:15.946Z [ERROR] ⚠️  High memory usage: 90.32%
2025-06-30T09:17:15.946Z [ERROR] ⚠️  High memory usage: 91.20%
2025-06-30T09:42:15.949Z [ERROR] ⚠️  High memory usage: 90.52%
2025-06-30T09:47:15.949Z [ERROR] ⚠️  High memory usage: 91.12%
2025-06-30T10:07:15.951Z [ERROR] ⚠️  High memory usage: 90.48%
2025-06-30T10:12:15.951Z [ERROR] ⚠️  High memory usage: 91.20%
2025-06-30T10:21:48.856Z [ERROR] [FFMPEG_STDERR] a27559ee-23c4-4df3-b67f-b0c36dc0baff: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/4vfp-pshz-g9cj-y5wz-bqsp: Broken pipe
2025-06-30T10:21:48.874Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream a27559ee-23c4-4df3-b67f-b0c36dc0baff
2025-06-30T10:22:35.965Z [ERROR] [FFMPEG_STDERR] ba5857c7-1ea6-4464-b60a-440ba8b877fc: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/murk-epb3-1gjj-v5df-9h70: Broken pipe
2025-06-30T10:22:35.985Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream ba5857c7-1ea6-4464-b60a-440ba8b877fc
2025-06-30T10:22:42.850Z [ERROR] [FFMPEG_STDERR] 8b70abdf-53a4-45c5-a6fd-80530205782c: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/7qkm-e8h9-cd4x-sq11-65gs: Broken pipe
2025-06-30T10:22:42.867Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8b70abdf-53a4-45c5-a6fd-80530205782c
2025-06-30T10:24:10.147Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-06-30T10:24:10.167Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-06-30T10:37:15.954Z [ERROR] ⚠️  High memory usage: 90.66%
2025-06-30T10:42:15.954Z [ERROR] ⚠️  High memory usage: 91.12%
2025-06-30T10:48:15.673Z [ERROR] [FFMPEG_STDERR] ba5857c7-1ea6-4464-b60a-440ba8b877fc: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/murk-epb3-1gjj-v5df-9h70: Broken pipe
2025-06-30T10:48:15.691Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream ba5857c7-1ea6-4464-b60a-440ba8b877fc
2025-06-30T10:50:29.083Z [ERROR] Error fetching current UTC time: connect ETIMEDOUT **************:443
2025-06-30T11:02:15.957Z [ERROR] ⚠️  High memory usage: 90.39%
2025-06-30T11:07:15.956Z [ERROR] ⚠️  High memory usage: 91.32%
2025-06-30T11:37:15.961Z [ERROR] ⚠️  High memory usage: 91.90%
2025-06-30T11:37:45.600Z [ERROR] [FFMPEG_STDERR] ba5857c7-1ea6-4464-b60a-440ba8b877fc: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/murk-epb3-1gjj-v5df-9h70: Broken pipe
2025-06-30T11:37:45.625Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream ba5857c7-1ea6-4464-b60a-440ba8b877fc
2025-06-30T11:57:15.964Z [ERROR] ⚠️  High memory usage: 90.90%
2025-06-30T12:02:15.964Z [ERROR] ⚠️  High memory usage: 92.20%
2025-06-30T12:07:15.965Z [ERROR] ⚠️  High memory usage: 93.09%
2025-06-30T12:32:15.968Z [ERROR] ⚠️  High memory usage: 91.81%
2025-06-30T12:57:15.969Z [ERROR] ⚠️  High memory usage: 90.62%
2025-06-30T13:02:15.969Z [ERROR] ⚠️  High memory usage: 91.64%
2025-06-30T13:27:15.970Z [ERROR] ⚠️  High memory usage: 90.81%
2025-06-30T13:32:15.970Z [ERROR] ⚠️  High memory usage: 91.51%
2025-06-30T13:52:15.971Z [ERROR] ⚠️  High memory usage: 90.11%
2025-06-30T13:57:15.972Z [ERROR] ⚠️  High memory usage: 90.73%
2025-06-30T14:22:15.974Z [ERROR] ⚠️  High memory usage: 90.66%
2025-06-30T14:27:15.975Z [ERROR] ⚠️  High memory usage: 91.61%
2025-06-30T14:52:15.976Z [ERROR] ⚠️  High memory usage: 90.81%
2025-06-30T15:17:15.978Z [ERROR] ⚠️  High memory usage: 91.08%
2025-06-30T15:47:15.983Z [ERROR] ⚠️  High memory usage: 92.74%
2025-06-30T15:57:15.984Z [ERROR] ⚠️  High memory usage: 92.52%
2025-06-30T16:22:15.986Z [ERROR] ⚠️  High memory usage: 90.85%
2025-06-30T16:27:15.987Z [ERROR] ⚠️  High memory usage: 92.12%
2025-06-30T16:47:15.989Z [ERROR] ⚠️  High memory usage: 91.17%
2025-06-30T17:17:15.991Z [ERROR] ⚠️  High memory usage: 91.42%
2025-06-30T17:47:15.994Z [ERROR] ⚠️  High memory usage: 91.38%
2025-06-30T18:27:15.996Z [ERROR] ⚠️  High memory usage: 91.51%
2025-06-30T18:47:15.995Z [ERROR] ⚠️  High memory usage: 90.14%
2025-06-30T18:52:15.995Z [ERROR] ⚠️  High memory usage: 91.13%
2025-06-30T18:57:15.996Z [ERROR] ⚠️  High memory usage: 91.40%
2025-06-30T19:17:15.997Z [ERROR] ⚠️  High memory usage: 90.31%
2025-06-30T19:22:15.997Z [ERROR] ⚠️  High memory usage: 91.24%
2025-06-30T19:47:15.998Z [ERROR] ⚠️  High memory usage: 91.01%
2025-06-30T20:12:16.001Z [ERROR] ⚠️  High memory usage: 90.45%
2025-06-30T20:17:16.001Z [ERROR] ⚠️  High memory usage: 91.54%
2025-06-30T20:42:16.003Z [ERROR] ⚠️  High memory usage: 90.69%
2025-06-30T21:12:16.005Z [ERROR] ⚠️  High memory usage: 90.79%
2025-06-30T21:32:16.007Z [ERROR] ⚠️  High memory usage: 90.21%
2025-06-30T21:37:16.007Z [ERROR] ⚠️  High memory usage: 90.32%
2025-06-30T21:42:16.008Z [ERROR] ⚠️  High memory usage: 91.13%
2025-06-30T22:02:16.007Z [ERROR] ⚠️  High memory usage: 90.04%
2025-06-30T22:07:16.008Z [ERROR] ⚠️  High memory usage: 91.41%
2025-06-30T22:32:16.009Z [ERROR] ⚠️  High memory usage: 91.16%
2025-06-30T22:47:16.010Z [ERROR] ⚠️  High memory usage: 90.67%
2025-06-30T23:07:16.012Z [ERROR] ⚠️  High memory usage: 92.46%
2025-06-30T23:17:16.013Z [ERROR] ⚠️  High memory usage: 90.04%
2025-06-30T23:22:16.014Z [ERROR] ⚠️  High memory usage: 93.44%
2025-06-30T23:42:16.015Z [ERROR] ⚠️  High memory usage: 91.59%
2025-06-30T23:47:16.016Z [ERROR] ⚠️  High memory usage: 92.57%
2025-07-01T00:12:16.017Z [ERROR] ⚠️  High memory usage: 90.54%
2025-07-01T00:42:16.018Z [ERROR] ⚠️  High memory usage: 90.64%
2025-07-01T01:07:16.020Z [ERROR] ⚠️  High memory usage: 90.49%
2025-07-01T01:12:16.020Z [ERROR] ⚠️  High memory usage: 91.32%
2025-07-01T01:37:16.024Z [ERROR] ⚠️  High memory usage: 90.38%
2025-07-01T02:07:16.025Z [ERROR] ⚠️  High memory usage: 90.39%
2025-07-01T02:32:16.027Z [ERROR] ⚠️  High memory usage: 90.46%
2025-07-01T03:02:16.030Z [ERROR] ⚠️  High memory usage: 90.58%
2025-07-01T03:15:57.945Z [ERROR] [FFMPEG_STDERR] ba5857c7-1ea6-4464-b60a-440ba8b877fc: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/murk-epb3-1gjj-v5df-9h70: Broken pipe
2025-07-01T03:15:57.970Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream ba5857c7-1ea6-4464-b60a-440ba8b877fc
2025-07-01T03:27:16.030Z [ERROR] ⚠️  High memory usage: 90.49%
2025-07-01T03:32:16.031Z [ERROR] ⚠️  High memory usage: 91.60%
2025-07-01T03:57:16.033Z [ERROR] ⚠️  High memory usage: 90.82%
2025-07-01T04:22:16.034Z [ERROR] ⚠️  High memory usage: 90.09%
2025-07-01T04:27:16.034Z [ERROR] ⚠️  High memory usage: 90.97%
2025-07-01T04:47:16.036Z [ERROR] ⚠️  High memory usage: 90.72%
2025-07-01T05:07:16.039Z [ERROR] ⚠️  High memory usage: 91.34%
2025-07-01T05:17:16.040Z [ERROR] ⚠️  High memory usage: 91.24%
2025-07-01T05:32:16.042Z [ERROR] ⚠️  High memory usage: 90.30%
2025-07-01T05:37:16.043Z [ERROR] ⚠️  High memory usage: 93.00%
2025-07-01T06:02:16.044Z [ERROR] ⚠️  High memory usage: 92.42%
2025-07-01T06:22:16.045Z [ERROR] ⚠️  High memory usage: 91.75%
2025-07-01T06:47:16.048Z [ERROR] ⚠️  High memory usage: 90.37%
2025-07-01T06:52:16.048Z [ERROR] ⚠️  High memory usage: 90.86%
2025-07-01T07:17:16.050Z [ERROR] ⚠️  High memory usage: 90.10%
2025-07-01T07:47:16.051Z [ERROR] ⚠️  High memory usage: 91.20%
2025-07-01T07:52:16.051Z [ERROR] ⚠️  High memory usage: 90.08%
2025-07-01T07:57:16.051Z [ERROR] ⚠️  High memory usage: 93.15%
2025-07-01T08:12:16.054Z [ERROR] ⚠️  High memory usage: 91.76%
2025-07-01T08:17:16.052Z [ERROR] ⚠️  High memory usage: 94.29%
2025-07-01T08:22:16.053Z [ERROR] ⚠️  High memory usage: 93.90%
2025-07-01T08:27:16.052Z [ERROR] ⚠️  High memory usage: 94.49%
2025-07-01T08:42:16.052Z [ERROR] ⚠️  High memory usage: 90.13%
2025-07-01T08:47:16.053Z [ERROR] ⚠️  High memory usage: 91.08%
2025-07-01T08:52:16.053Z [ERROR] ⚠️  High memory usage: 92.05%
2025-07-01T08:57:16.053Z [ERROR] ⚠️  High memory usage: 93.30%
2025-07-01T09:12:16.055Z [ERROR] ⚠️  High memory usage: 90.61%
2025-07-01T09:17:16.055Z [ERROR] ⚠️  High memory usage: 91.93%
2025-07-01T09:22:16.056Z [ERROR] ⚠️  High memory usage: 93.04%
2025-07-01T09:27:16.056Z [ERROR] ⚠️  High memory usage: 93.57%
2025-07-01T09:42:16.056Z [ERROR] ⚠️  High memory usage: 91.03%
2025-07-01T09:47:16.056Z [ERROR] ⚠️  High memory usage: 92.26%
2025-07-01T09:52:16.056Z [ERROR] ⚠️  High memory usage: 93.01%
2025-07-01T09:57:16.057Z [ERROR] ⚠️  High memory usage: 94.18%
2025-07-01T10:07:16.058Z [ERROR] ⚠️  High memory usage: 90.50%
2025-07-01T10:12:16.059Z [ERROR] ⚠️  High memory usage: 93.50%
2025-07-01T10:17:16.059Z [ERROR] ⚠️  High memory usage: 90.89%
2025-07-01T10:22:16.059Z [ERROR] ⚠️  High memory usage: 94.02%
2025-07-01T10:32:16.061Z [ERROR] ⚠️  High memory usage: 91.47%
2025-07-01T10:42:16.063Z [ERROR] ⚠️  High memory usage: 91.00%
2025-07-01T10:47:16.063Z [ERROR] ⚠️  High memory usage: 93.35%
2025-07-01T10:57:16.064Z [ERROR] ⚠️  High memory usage: 92.06%
2025-07-01T11:02:16.064Z [ERROR] ⚠️  High memory usage: 94.29%
2025-07-01T11:12:16.066Z [ERROR] ⚠️  High memory usage: 91.80%
2025-07-01T11:22:16.068Z [ERROR] ⚠️  High memory usage: 90.42%
2025-07-01T11:27:16.069Z [ERROR] ⚠️  High memory usage: 93.00%
2025-07-01T11:37:16.069Z [ERROR] ⚠️  High memory usage: 91.29%
2025-07-01T11:42:16.069Z [ERROR] ⚠️  High memory usage: 93.77%
2025-07-01T11:52:16.070Z [ERROR] ⚠️  High memory usage: 91.81%
2025-07-01T12:02:16.071Z [ERROR] ⚠️  High memory usage: 92.40%
2025-07-01T12:12:16.073Z [ERROR] ⚠️  High memory usage: 91.25%
2025-07-01T12:17:16.074Z [ERROR] ⚠️  High memory usage: 92.29%
2025-07-01T12:22:16.075Z [ERROR] ⚠️  High memory usage: 93.39%
2025-07-01T12:42:16.077Z [ERROR] ⚠️  High memory usage: 90.50%
2025-07-01T12:47:16.077Z [ERROR] ⚠️  High memory usage: 90.81%
2025-07-01T12:52:16.078Z [ERROR] ⚠️  High memory usage: 92.77%
2025-07-01T13:07:16.080Z [ERROR] ⚠️  High memory usage: 90.27%
2025-07-01T13:12:16.080Z [ERROR] ⚠️  High memory usage: 91.01%
2025-07-01T13:17:16.081Z [ERROR] ⚠️  High memory usage: 91.85%
2025-07-01T13:22:16.081Z [ERROR] ⚠️  High memory usage: 92.42%
2025-07-01T13:37:16.084Z [ERROR] ⚠️  High memory usage: 90.52%
2025-07-01T13:42:16.085Z [ERROR] ⚠️  High memory usage: 91.21%
2025-07-01T13:47:16.084Z [ERROR] ⚠️  High memory usage: 91.91%
2025-07-01T14:17:16.084Z [ERROR] ⚠️  High memory usage: 90.80%
2025-07-01T14:27:16.085Z [ERROR] ⚠️  High memory usage: 91.24%
2025-07-01T14:32:16.086Z [ERROR] ⚠️  High memory usage: 93.08%
2025-07-01T14:52:16.087Z [ERROR] ⚠️  High memory usage: 90.20%
2025-07-01T14:57:16.088Z [ERROR] ⚠️  High memory usage: 90.73%
2025-07-01T15:02:16.089Z [ERROR] ⚠️  High memory usage: 91.09%
2025-07-01T15:22:16.089Z [ERROR] ⚠️  High memory usage: 90.12%
2025-07-01T15:27:16.090Z [ERROR] ⚠️  High memory usage: 90.95%
2025-07-01T15:32:16.090Z [ERROR] ⚠️  High memory usage: 91.99%
2025-07-01T15:52:16.092Z [ERROR] ⚠️  High memory usage: 90.58%
2025-07-01T15:57:16.093Z [ERROR] ⚠️  High memory usage: 91.45%
2025-07-01T16:02:16.094Z [ERROR] ⚠️  High memory usage: 92.39%
2025-07-01T16:22:16.097Z [ERROR] ⚠️  High memory usage: 90.89%
2025-07-01T16:27:16.097Z [ERROR] ⚠️  High memory usage: 92.18%
2025-07-01T16:47:16.099Z [ERROR] ⚠️  High memory usage: 90.24%
2025-07-01T16:52:16.099Z [ERROR] ⚠️  High memory usage: 90.94%
2025-07-01T16:57:16.099Z [ERROR] ⚠️  High memory usage: 92.02%
2025-07-01T17:22:16.100Z [ERROR] ⚠️  High memory usage: 91.51%
2025-07-01T17:27:16.101Z [ERROR] ⚠️  High memory usage: 91.89%
2025-07-01T17:47:16.102Z [ERROR] ⚠️  High memory usage: 90.54%
2025-07-01T17:52:16.102Z [ERROR] ⚠️  High memory usage: 91.59%
2025-07-01T18:12:16.104Z [ERROR] ⚠️  High memory usage: 90.11%
2025-07-01T18:17:16.105Z [ERROR] ⚠️  High memory usage: 90.43%
2025-07-01T18:22:16.106Z [ERROR] ⚠️  High memory usage: 90.80%
2025-07-01T18:47:16.109Z [ERROR] ⚠️  High memory usage: 91.52%
2025-07-01T18:52:16.109Z [ERROR] ⚠️  High memory usage: 92.43%
2025-07-01T19:12:16.112Z [ERROR] ⚠️  High memory usage: 90.41%
2025-07-01T19:17:16.112Z [ERROR] ⚠️  High memory usage: 91.17%
2025-07-01T19:22:16.113Z [ERROR] ⚠️  High memory usage: 92.63%
2025-07-01T19:42:16.115Z [ERROR] ⚠️  High memory usage: 90.80%
2025-07-01T19:47:16.115Z [ERROR] ⚠️  High memory usage: 91.02%
2025-07-01T19:52:16.116Z [ERROR] ⚠️  High memory usage: 92.66%
2025-07-01T20:12:16.116Z [ERROR] ⚠️  High memory usage: 91.33%
2025-07-01T20:17:16.117Z [ERROR] ⚠️  High memory usage: 91.62%
2025-07-01T20:37:16.120Z [ERROR] ⚠️  High memory usage: 90.63%
2025-07-01T20:42:16.120Z [ERROR] ⚠️  High memory usage: 91.31%
2025-07-01T20:47:16.120Z [ERROR] ⚠️  High memory usage: 92.30%
2025-07-01T21:02:16.121Z [ERROR] ⚠️  High memory usage: 90.01%
2025-07-01T21:07:16.122Z [ERROR] ⚠️  High memory usage: 90.40%
2025-07-01T21:12:16.123Z [ERROR] ⚠️  High memory usage: 91.42%
2025-07-01T21:32:16.126Z [ERROR] ⚠️  High memory usage: 90.24%
2025-07-01T21:37:16.127Z [ERROR] ⚠️  High memory usage: 90.56%
2025-07-01T21:42:16.127Z [ERROR] ⚠️  High memory usage: 92.25%
2025-07-01T21:57:16.127Z [ERROR] ⚠️  High memory usage: 90.13%
2025-07-01T22:02:16.128Z [ERROR] ⚠️  High memory usage: 91.02%
2025-07-01T22:07:16.128Z [ERROR] ⚠️  High memory usage: 92.39%
2025-07-01T22:22:16.129Z [ERROR] ⚠️  High memory usage: 90.24%
2025-07-01T22:27:16.130Z [ERROR] ⚠️  High memory usage: 90.77%
2025-07-01T22:32:16.131Z [ERROR] ⚠️  High memory usage: 91.93%
2025-07-01T22:37:16.132Z [ERROR] ⚠️  High memory usage: 91.92%
2025-07-01T23:02:16.134Z [ERROR] ⚠️  High memory usage: 91.96%
2025-07-01T23:17:16.135Z [ERROR] ⚠️  High memory usage: 92.49%
2025-07-01T23:27:16.136Z [ERROR] ⚠️  High memory usage: 90.35%
2025-07-01T23:32:16.137Z [ERROR] ⚠️  High memory usage: 91.20%
2025-07-01T23:37:16.137Z [ERROR] ⚠️  High memory usage: 91.60%
2025-07-01T23:42:16.137Z [ERROR] ⚠️  High memory usage: 92.05%
2025-07-02T00:02:16.139Z [ERROR] ⚠️  High memory usage: 90.26%
2025-07-02T00:07:16.140Z [ERROR] ⚠️  High memory usage: 91.17%
2025-07-02T00:32:16.142Z [ERROR] ⚠️  High memory usage: 91.04%
2025-07-02T00:37:16.142Z [ERROR] ⚠️  High memory usage: 91.60%
2025-07-02T00:57:16.144Z [ERROR] ⚠️  High memory usage: 90.54%
2025-07-02T01:02:16.144Z [ERROR] ⚠️  High memory usage: 91.27%
2025-07-02T01:07:16.144Z [ERROR] ⚠️  High memory usage: 91.55%
2025-07-02T01:27:16.144Z [ERROR] ⚠️  High memory usage: 90.49%
2025-07-02T01:32:16.144Z [ERROR] ⚠️  High memory usage: 91.36%
2025-07-02T01:37:16.144Z [ERROR] ⚠️  High memory usage: 91.79%
2025-07-02T01:57:16.147Z [ERROR] ⚠️  High memory usage: 90.54%
2025-07-02T02:02:16.148Z [ERROR] ⚠️  High memory usage: 91.37%
2025-07-02T02:27:16.149Z [ERROR] ⚠️  High memory usage: 90.70%
2025-07-02T02:32:16.149Z [ERROR] ⚠️  High memory usage: 91.61%
2025-07-02T02:52:16.150Z [ERROR] ⚠️  High memory usage: 91.69%
2025-07-02T02:57:16.153Z [ERROR] ⚠️  High memory usage: 91.86%
2025-07-02T03:02:16.153Z [ERROR] ⚠️  High memory usage: 92.05%
2025-07-02T03:07:16.154Z [ERROR] ⚠️  High memory usage: 91.66%
2025-07-02T03:12:16.155Z [ERROR] ⚠️  High memory usage: 92.31%
2025-07-02T03:17:16.154Z [ERROR] ⚠️  High memory usage: 90.74%
2025-07-02T03:22:16.154Z [ERROR] ⚠️  High memory usage: 92.26%
2025-07-02T03:37:16.154Z [ERROR] ⚠️  High memory usage: 93.93%
2025-07-02T03:42:16.154Z [ERROR] ⚠️  High memory usage: 92.37%
2025-07-02T03:47:16.154Z [ERROR] ⚠️  High memory usage: 94.51%
2025-07-02T03:57:16.154Z [ERROR] ⚠️  High memory usage: 91.99%
2025-07-02T04:17:16.155Z [ERROR] ⚠️  High memory usage: 90.76%
2025-07-02T04:22:16.156Z [ERROR] ⚠️  High memory usage: 91.68%
2025-07-02T04:27:16.157Z [ERROR] ⚠️  High memory usage: 90.78%
2025-07-02T04:37:16.159Z [ERROR] ⚠️  High memory usage: 92.36%
2025-07-02T04:47:16.162Z [ERROR] ⚠️  High memory usage: 90.46%
2025-07-02T04:52:16.162Z [ERROR] ⚠️  High memory usage: 93.10%
2025-07-02T05:02:16.164Z [ERROR] ⚠️  High memory usage: 91.52%
2025-07-02T05:22:16.164Z [ERROR] ⚠️  High memory usage: 94.75%
2025-07-02T05:27:16.165Z [ERROR] ⚠️  High memory usage: 94.63%
2025-07-02T05:32:16.165Z [ERROR] ⚠️  High memory usage: 94.86%
2025-07-02T05:37:16.165Z [ERROR] ⚠️  High memory usage: 92.78%
2025-07-02T05:42:16.165Z [ERROR] ⚠️  High memory usage: 94.91%
2025-07-02T05:52:16.167Z [ERROR] ⚠️  High memory usage: 95.04%
2025-07-02T05:57:16.167Z [ERROR] ⚠️  High memory usage: 90.12%
2025-07-02T06:02:16.168Z [ERROR] ⚠️  High memory usage: 92.42%
2025-07-02T06:12:16.169Z [ERROR] ⚠️  High memory usage: 90.67%
2025-07-02T06:17:16.169Z [ERROR] ⚠️  High memory usage: 94.31%
2025-07-02T06:22:16.169Z [ERROR] ⚠️  High memory usage: 90.17%
2025-07-02T06:27:16.170Z [ERROR] ⚠️  High memory usage: 93.32%
2025-07-02T06:37:16.171Z [ERROR] ⚠️  High memory usage: 92.64%
2025-07-02T06:47:16.171Z [ERROR] ⚠️  High memory usage: 90.34%
2025-07-02T06:52:16.171Z [ERROR] ⚠️  High memory usage: 92.87%
2025-07-02T07:02:16.172Z [ERROR] ⚠️  High memory usage: 91.86%
2025-07-02T07:12:16.174Z [ERROR] ⚠️  High memory usage: 90.33%
2025-07-02T07:17:16.174Z [ERROR] ⚠️  High memory usage: 93.09%
2025-07-02T07:27:16.174Z [ERROR] ⚠️  High memory usage: 91.88%
2025-07-02T07:37:16.174Z [ERROR] ⚠️  High memory usage: 90.68%
2025-07-02T07:42:16.174Z [ERROR] ⚠️  High memory usage: 94.15%
2025-07-02T07:52:16.175Z [ERROR] ⚠️  High memory usage: 92.36%
2025-07-02T08:02:16.176Z [ERROR] ⚠️  High memory usage: 91.03%
2025-07-02T08:27:16.178Z [ERROR] ⚠️  High memory usage: 92.48%
2025-07-02T08:32:16.179Z [ERROR] ⚠️  High memory usage: 93.30%
2025-07-02T08:37:16.179Z [ERROR] ⚠️  High memory usage: 92.91%
2025-07-02T08:42:16.179Z [ERROR] ⚠️  High memory usage: 90.16%
2025-07-02T08:47:16.179Z [ERROR] ⚠️  High memory usage: 90.19%
2025-07-02T08:57:16.180Z [ERROR] ⚠️  High memory usage: 93.89%
2025-07-02T09:02:16.179Z [ERROR] ⚠️  High memory usage: 90.88%
2025-07-02T09:12:16.179Z [ERROR] ⚠️  High memory usage: 92.94%
2025-07-02T09:17:16.179Z [ERROR] ⚠️  High memory usage: 91.63%
2025-07-02T09:32:16.181Z [ERROR] ⚠️  High memory usage: 91.52%
2025-07-02T09:47:16.183Z [ERROR] ⚠️  High memory usage: 91.28%
2025-07-02T09:52:16.183Z [ERROR] ⚠️  High memory usage: 91.09%
2025-07-02T10:07:16.184Z [ERROR] ⚠️  High memory usage: 90.76%
2025-07-02T10:17:16.184Z [ERROR] ⚠️  High memory usage: 93.42%
2025-07-02T10:22:16.184Z [ERROR] ⚠️  High memory usage: 91.43%
2025-07-02T10:37:16.184Z [ERROR] ⚠️  High memory usage: 90.68%
2025-07-02T10:42:16.184Z [ERROR] ⚠️  High memory usage: 91.32%
2025-07-02T10:47:16.184Z [ERROR] ⚠️  High memory usage: 92.28%
2025-07-02T11:37:16.189Z [ERROR] ⚠️  High memory usage: 90.99%
2025-07-02T12:02:16.192Z [ERROR] ⚠️  High memory usage: 90.27%
2025-07-02T12:07:16.192Z [ERROR] ⚠️  High memory usage: 90.88%
2025-07-02T12:37:16.193Z [ERROR] ⚠️  High memory usage: 90.45%
2025-07-02T13:02:16.194Z [ERROR] ⚠️  High memory usage: 90.39%
2025-07-02T13:07:16.194Z [ERROR] ⚠️  High memory usage: 91.41%
2025-07-02T13:17:16.195Z [ERROR] ⚠️  High memory usage: 90.50%
2025-07-02T13:42:16.196Z [ERROR] ⚠️  High memory usage: 90.53%
2025-07-02T13:47:16.196Z [ERROR] ⚠️  High memory usage: 91.63%
2025-07-02T14:07:16.200Z [ERROR] ⚠️  High memory usage: 90.06%
2025-07-02T14:12:16.199Z [ERROR] ⚠️  High memory usage: 90.43%
2025-07-02T14:42:16.203Z [ERROR] ⚠️  High memory usage: 91.10%
2025-07-02T15:12:16.205Z [ERROR] ⚠️  High memory usage: 91.02%
2025-07-02T15:37:16.206Z [ERROR] ⚠️  High memory usage: 91.49%
2025-07-02T16:02:16.211Z [ERROR] ⚠️  High memory usage: 90.42%
2025-07-02T16:07:16.211Z [ERROR] ⚠️  High memory usage: 90.62%
2025-07-02T16:27:16.213Z [ERROR] ⚠️  High memory usage: 90.05%
2025-07-02T16:32:16.214Z [ERROR] ⚠️  High memory usage: 91.20%
2025-07-02T17:02:16.217Z [ERROR] ⚠️  High memory usage: 91.10%
2025-07-02T17:22:16.219Z [ERROR] ⚠️  High memory usage: 90.10%
2025-07-02T17:27:16.219Z [ERROR] ⚠️  High memory usage: 90.95%
2025-07-02T17:52:16.219Z [ERROR] ⚠️  High memory usage: 90.59%
2025-07-02T18:17:16.221Z [ERROR] ⚠️  High memory usage: 91.12%
2025-07-02T18:47:16.223Z [ERROR] ⚠️  High memory usage: 90.58%
2025-07-02T19:07:16.224Z [ERROR] ⚠️  High memory usage: 90.41%
2025-07-02T19:12:16.224Z [ERROR] ⚠️  High memory usage: 90.62%
2025-07-02T19:32:16.226Z [ERROR] ⚠️  High memory usage: 90.42%
2025-07-02T19:37:16.225Z [ERROR] ⚠️  High memory usage: 90.57%
2025-07-02T20:02:16.228Z [ERROR] ⚠️  High memory usage: 91.15%
2025-07-02T20:27:16.230Z [ERROR] ⚠️  High memory usage: 90.68%
2025-07-02T20:32:16.230Z [ERROR] ⚠️  High memory usage: 91.93%
2025-07-02T20:52:16.231Z [ERROR] ⚠️  High memory usage: 90.49%
2025-07-02T20:57:16.232Z [ERROR] ⚠️  High memory usage: 91.11%
2025-07-02T21:22:16.232Z [ERROR] ⚠️  High memory usage: 90.38%
2025-07-02T21:27:16.232Z [ERROR] ⚠️  High memory usage: 91.15%
2025-07-02T21:47:16.233Z [ERROR] ⚠️  High memory usage: 91.62%
2025-07-02T22:12:16.236Z [ERROR] ⚠️  High memory usage: 90.20%
2025-07-02T22:17:16.236Z [ERROR] ⚠️  High memory usage: 90.73%
2025-07-02T22:42:16.238Z [ERROR] ⚠️  High memory usage: 90.35%
2025-07-02T22:47:16.239Z [ERROR] ⚠️  High memory usage: 92.02%
2025-07-02T23:12:16.240Z [ERROR] ⚠️  High memory usage: 92.13%
2025-07-02T23:17:16.241Z [ERROR] ⚠️  High memory usage: 94.45%
2025-07-02T23:22:16.242Z [ERROR] ⚠️  High memory usage: 94.78%
2025-07-02T23:27:16.243Z [ERROR] ⚠️  High memory usage: 95.04%
2025-07-02T23:37:16.244Z [ERROR] ⚠️  High memory usage: 90.62%
2025-07-02T23:42:16.244Z [ERROR] ⚠️  High memory usage: 91.76%
2025-07-02T23:47:16.244Z [ERROR] ⚠️  High memory usage: 92.78%
2025-07-02T23:57:16.246Z [ERROR] ⚠️  High memory usage: 91.07%
2025-07-03T00:02:16.246Z [ERROR] ⚠️  High memory usage: 92.00%
2025-07-03T00:07:16.247Z [ERROR] ⚠️  High memory usage: 92.96%
2025-07-03T00:12:16.247Z [ERROR] ⚠️  High memory usage: 93.61%
2025-07-03T00:17:16.248Z [ERROR] ⚠️  High memory usage: 94.15%
2025-07-03T00:32:16.249Z [ERROR] ⚠️  High memory usage: 90.33%
2025-07-03T00:37:16.248Z [ERROR] ⚠️  High memory usage: 91.34%
2025-07-03T00:42:16.249Z [ERROR] ⚠️  High memory usage: 92.88%
2025-07-03T00:47:16.250Z [ERROR] ⚠️  High memory usage: 93.10%
2025-07-03T00:57:16.251Z [ERROR] ⚠️  High memory usage: 90.30%
2025-07-03T01:02:16.253Z [ERROR] ⚠️  High memory usage: 91.24%
2025-07-03T01:07:16.254Z [ERROR] ⚠️  High memory usage: 91.79%
2025-07-03T01:12:16.254Z [ERROR] ⚠️  High memory usage: 93.03%
2025-07-03T01:17:16.255Z [ERROR] ⚠️  High memory usage: 94.00%
2025-07-03T01:32:16.257Z [ERROR] ⚠️  High memory usage: 90.85%
2025-07-03T01:37:16.257Z [ERROR] ⚠️  High memory usage: 92.12%
2025-07-03T01:42:16.257Z [ERROR] ⚠️  High memory usage: 92.41%
2025-07-03T01:47:16.257Z [ERROR] ⚠️  High memory usage: 93.54%
2025-07-03T01:57:16.257Z [ERROR] ⚠️  High memory usage: 90.56%
2025-07-03T02:02:16.257Z [ERROR] ⚠️  High memory usage: 91.28%
2025-07-03T02:07:16.258Z [ERROR] ⚠️  High memory usage: 92.07%
2025-07-03T02:12:16.258Z [ERROR] ⚠️  High memory usage: 93.26%
2025-07-03T02:22:16.259Z [ERROR] ⚠️  High memory usage: 90.29%
2025-07-03T02:27:16.259Z [ERROR] ⚠️  High memory usage: 91.19%
2025-07-03T02:32:16.259Z [ERROR] ⚠️  High memory usage: 92.08%
2025-07-03T02:37:16.259Z [ERROR] ⚠️  High memory usage: 93.04%
2025-07-03T02:42:16.260Z [ERROR] ⚠️  High memory usage: 93.83%
2025-07-03T02:47:16.260Z [ERROR] ⚠️  High memory usage: 90.53%
2025-07-03T02:52:16.260Z [ERROR] ⚠️  High memory usage: 94.05%
2025-07-03T02:57:16.260Z [ERROR] ⚠️  High memory usage: 91.72%
2025-07-03T03:02:16.260Z [ERROR] ⚠️  High memory usage: 93.08%
2025-07-03T03:07:16.260Z [ERROR] ⚠️  High memory usage: 90.15%
2025-07-03T03:12:16.261Z [ERROR] ⚠️  High memory usage: 93.73%
2025-07-03T03:22:16.263Z [ERROR] ⚠️  High memory usage: 91.54%
2025-07-03T03:37:16.266Z [ERROR] ⚠️  High memory usage: 92.78%
2025-07-03T03:47:16.266Z [ERROR] ⚠️  High memory usage: 92.10%
2025-07-03T03:52:16.267Z [ERROR] ⚠️  High memory usage: 90.08%
2025-07-03T03:57:16.267Z [ERROR] ⚠️  High memory usage: 92.32%
2025-07-03T04:21:08.229Z [ERROR] [FFMPEG_STDERR] 28b8c843-fc78-4a81-9868-f7fe22d0ef55: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/b4w4-hqbw-amy5-8res-0tfx: Broken pipe
2025-07-03T04:21:08.249Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 28b8c843-fc78-4a81-9868-f7fe22d0ef55
2025-07-03T04:22:16.268Z [ERROR] ⚠️  High memory usage: 90.03%
2025-07-03T04:27:16.269Z [ERROR] ⚠️  High memory usage: 91.36%
2025-07-03T04:57:16.271Z [ERROR] ⚠️  High memory usage: 90.97%
2025-07-03T05:22:16.271Z [ERROR] ⚠️  High memory usage: 90.91%
2025-07-03T05:47:25.065Z [ERROR] Error fetching current UTC time: getaddrinfo EAI_AGAIN timeapi.io
2025-07-03T05:47:35.109Z [ERROR] Error fetching current UTC time: getaddrinfo EAI_AGAIN timeapi.io
2025-07-03T05:52:16.271Z [ERROR] ⚠️  High memory usage: 93.26%
2025-07-03T06:17:16.274Z [ERROR] ⚠️  High memory usage: 91.02%
2025-07-03T06:42:16.278Z [ERROR] ⚠️  High memory usage: 91.03%
2025-07-03T06:46:06.526Z [ERROR] [FFMPEG_STDERR] ba5857c7-1ea6-4464-b60a-440ba8b877fc: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/murk-epb3-1gjj-v5df-9h70: Broken pipe
2025-07-03T06:46:06.563Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream ba5857c7-1ea6-4464-b60a-440ba8b877fc
2025-07-03T06:47:16.278Z [ERROR] ⚠️  High memory usage: 91.68%
2025-07-03T07:07:16.280Z [ERROR] ⚠️  High memory usage: 90.79%
2025-07-03T07:12:16.281Z [ERROR] ⚠️  High memory usage: 91.88%
2025-07-03T07:37:16.283Z [ERROR] ⚠️  High memory usage: 90.83%
2025-07-03T07:42:16.283Z [ERROR] ⚠️  High memory usage: 92.40%
2025-07-03T08:02:16.284Z [ERROR] ⚠️  High memory usage: 90.03%
2025-07-03T08:07:16.285Z [ERROR] ⚠️  High memory usage: 90.89%
2025-07-03T08:12:16.285Z [ERROR] ⚠️  High memory usage: 91.71%
2025-07-03T08:32:16.285Z [ERROR] ⚠️  High memory usage: 90.48%
2025-07-03T08:37:16.285Z [ERROR] ⚠️  High memory usage: 90.92%
2025-07-03T08:42:16.285Z [ERROR] ⚠️  High memory usage: 91.62%
2025-07-03T08:47:16.285Z [ERROR] ⚠️  High memory usage: 93.78%
2025-07-03T08:52:16.286Z [ERROR] ⚠️  High memory usage: 90.26%
2025-07-03T08:57:16.286Z [ERROR] ⚠️  High memory usage: 92.88%
2025-07-03T09:27:16.288Z [ERROR] ⚠️  High memory usage: 92.43%
2025-07-03T09:37:16.290Z [ERROR] ⚠️  High memory usage: 90.92%
2025-07-03T09:47:16.290Z [ERROR] ⚠️  High memory usage: 90.08%
2025-07-03T09:52:16.290Z [ERROR] ⚠️  High memory usage: 92.93%
2025-07-03T10:02:16.291Z [ERROR] ⚠️  High memory usage: 90.96%
2025-07-03T10:27:16.298Z [ERROR] ⚠️  High memory usage: 90.42%
2025-07-03T10:49:52.948Z [ERROR] [FFMPEG_STDERR] ba5857c7-1ea6-4464-b60a-440ba8b877fc: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/murk-epb3-1gjj-v5df-9h70: Broken pipe
2025-07-03T10:49:52.975Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream ba5857c7-1ea6-4464-b60a-440ba8b877fc
2025-07-03T10:52:16.300Z [ERROR] ⚠️  High memory usage: 90.50%
2025-07-03T11:02:16.302Z [ERROR] ⚠️  High memory usage: 90.63%
2025-07-03T11:17:16.303Z [ERROR] ⚠️  High memory usage: 90.89%
2025-07-03T11:19:13.322Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-07-03T11:19:13.340Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-07-03T11:19:48.654Z [ERROR] [FFMPEG_STDERR] 282fec21-3512-4f0e-a193-c7a0fb918dfe: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/fc4e-41fg-d62d-4u4x-62t8: Broken pipe
2025-07-03T11:19:48.670Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 282fec21-3512-4f0e-a193-c7a0fb918dfe
2025-07-03T11:27:16.304Z [ERROR] ⚠️  High memory usage: 90.01%
2025-07-03T11:29:17.897Z [ERROR] [FFMPEG_STDERR] ba5857c7-1ea6-4464-b60a-440ba8b877fc: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/murk-epb3-1gjj-v5df-9h70: Broken pipe
2025-07-03T11:29:17.921Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream ba5857c7-1ea6-4464-b60a-440ba8b877fc
2025-07-03T11:38:00.517Z [ERROR] [FFMPEG_STDERR] 282fec21-3512-4f0e-a193-c7a0fb918dfe: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/fc4e-41fg-d62d-4u4x-62t8: Broken pipe
2025-07-03T11:38:00.534Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 282fec21-3512-4f0e-a193-c7a0fb918dfe
2025-07-03T11:41:27.756Z [ERROR] [FFMPEG_STDERR] ba5857c7-1ea6-4464-b60a-440ba8b877fc: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/murk-epb3-1gjj-v5df-9h70: Broken pipe
2025-07-03T11:41:27.775Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream ba5857c7-1ea6-4464-b60a-440ba8b877fc
2025-07-03T11:42:16.305Z [ERROR] ⚠️  High memory usage: 91.29%
2025-07-03T11:52:16.307Z [ERROR] ⚠️  High memory usage: 90.58%
2025-07-03T12:04:11.798Z [ERROR] [FFMPEG_STDERR] 282fec21-3512-4f0e-a193-c7a0fb918dfe: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/fc4e-41fg-d62d-4u4x-62t8: Broken pipe
2025-07-03T12:04:11.824Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 282fec21-3512-4f0e-a193-c7a0fb918dfe
2025-07-03T12:06:03.658Z [ERROR] [FFMPEG_STDERR] 282fec21-3512-4f0e-a193-c7a0fb918dfe: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/fc4e-41fg-d62d-4u4x-62t8: Broken pipe
2025-07-03T12:06:03.671Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 282fec21-3512-4f0e-a193-c7a0fb918dfe
2025-07-03T12:07:16.307Z [ERROR] ⚠️  High memory usage: 92.07%
2025-07-03T12:17:16.307Z [ERROR] ⚠️  High memory usage: 90.50%
2025-07-03T12:17:48.655Z [ERROR] [FFMPEG_STDERR] 28b8c843-fc78-4a81-9868-f7fe22d0ef55: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/b4w4-hqbw-amy5-8res-0tfx: Broken pipe
2025-07-03T12:17:48.670Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 28b8c843-fc78-4a81-9868-f7fe22d0ef55
2025-07-03T12:19:10.611Z [ERROR] [FFMPEG_STDERR] 9068a775-9617-40e2-a473-800022bc6b03: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/7qkm-e8h9-cd4x-sq11-65gs: Broken pipe
2025-07-03T12:19:10.635Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 9068a775-9617-40e2-a473-800022bc6b03
2025-07-03T12:27:16.308Z [ERROR] ⚠️  High memory usage: 90.02%
2025-07-03T12:27:41.762Z [ERROR] [FFMPEG_STDERR] 282fec21-3512-4f0e-a193-c7a0fb918dfe: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/fc4e-41fg-d62d-4u4x-62t8: Broken pipe
2025-07-03T12:27:41.786Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 282fec21-3512-4f0e-a193-c7a0fb918dfe
2025-07-03T12:32:13.303Z [ERROR] [FFMPEG_STDERR] 9068a775-9617-40e2-a473-800022bc6b03: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/7qkm-e8h9-cd4x-sq11-65gs: Broken pipe
2025-07-03T12:32:13.321Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 9068a775-9617-40e2-a473-800022bc6b03
2025-07-03T12:39:58.130Z [ERROR] [FFMPEG_STDERR] ba5857c7-1ea6-4464-b60a-440ba8b877fc: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/murk-epb3-1gjj-v5df-9h70: Connection reset by peer
2025-07-03T12:39:58.151Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream ba5857c7-1ea6-4464-b60a-440ba8b877fc
2025-07-03T12:52:16.308Z [ERROR] ⚠️  High memory usage: 90.69%
2025-07-03T12:57:16.308Z [ERROR] ⚠️  High memory usage: 91.59%
2025-07-03T13:00:59.478Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-07-03T13:00:59.499Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-07-03T13:07:16.308Z [ERROR] ⚠️  High memory usage: 92.13%
2025-07-03T13:12:16.308Z [ERROR] ⚠️  High memory usage: 92.30%
2025-07-03T13:17:16.308Z [ERROR] ⚠️  High memory usage: 92.35%
2025-07-03T13:17:34.283Z [ERROR] [FFMPEG_STDERR] 282fec21-3512-4f0e-a193-c7a0fb918dfe: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/fc4e-41fg-d62d-4u4x-62t8: Connection reset by peer
2025-07-03T13:17:34.323Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 282fec21-3512-4f0e-a193-c7a0fb918dfe
2025-07-03T13:22:16.308Z [ERROR] ⚠️  High memory usage: 91.93%
2025-07-03T13:27:16.308Z [ERROR] ⚠️  High memory usage: 93.24%
2025-07-03T13:32:16.308Z [ERROR] ⚠️  High memory usage: 90.54%
2025-07-03T13:37:16.308Z [ERROR] ⚠️  High memory usage: 92.90%
2025-07-03T13:47:16.309Z [ERROR] ⚠️  High memory usage: 92.48%
2025-07-03T13:52:35.495Z [ERROR] [FFMPEG_STDERR] ae8f8bdd-f37f-4a62-b6eb-760571bc86a2: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/sqew-1dvz-e9gr-adg4-2sqm: Broken pipe
2025-07-03T13:52:35.505Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream ae8f8bdd-f37f-4a62-b6eb-760571bc86a2
2025-07-03T14:02:16.313Z [ERROR] ⚠️  High memory usage: 91.08%
2025-07-03T14:07:16.313Z [ERROR] ⚠️  High memory usage: 92.16%
2025-07-03T14:12:16.313Z [ERROR] ⚠️  High memory usage: 91.64%
2025-07-03T14:17:16.313Z [ERROR] ⚠️  High memory usage: 94.26%
2025-07-03T14:32:16.314Z [ERROR] ⚠️  High memory usage: 90.01%
2025-07-03T14:37:16.315Z [ERROR] ⚠️  High memory usage: 91.19%
2025-07-03T14:42:16.316Z [ERROR] ⚠️  High memory usage: 92.04%
2025-07-03T15:07:16.317Z [ERROR] ⚠️  High memory usage: 90.98%
2025-07-03T15:12:16.317Z [ERROR] ⚠️  High memory usage: 92.45%
2025-07-03T15:32:16.319Z [ERROR] ⚠️  High memory usage: 90.14%
2025-07-03T15:37:16.319Z [ERROR] ⚠️  High memory usage: 90.99%
2025-07-03T15:42:16.319Z [ERROR] ⚠️  High memory usage: 91.75%
2025-07-03T16:02:16.321Z [ERROR] ⚠️  High memory usage: 90.25%
2025-07-03T16:07:16.322Z [ERROR] ⚠️  High memory usage: 91.18%
2025-07-03T16:12:16.323Z [ERROR] ⚠️  High memory usage: 92.09%
2025-07-03T16:32:16.326Z [ERROR] ⚠️  High memory usage: 91.22%
2025-07-03T16:37:16.326Z [ERROR] ⚠️  High memory usage: 91.78%
2025-07-03T16:57:16.329Z [ERROR] ⚠️  High memory usage: 90.76%
2025-07-03T17:02:16.330Z [ERROR] ⚠️  High memory usage: 91.67%
2025-07-03T17:27:16.333Z [ERROR] ⚠️  High memory usage: 91.22%
2025-07-03T17:32:16.333Z [ERROR] ⚠️  High memory usage: 92.28%
2025-07-03T17:52:16.335Z [ERROR] ⚠️  High memory usage: 90.60%
2025-07-03T17:57:16.335Z [ERROR] ⚠️  High memory usage: 91.96%
2025-07-03T18:17:16.337Z [ERROR] ⚠️  High memory usage: 90.47%
2025-07-03T18:22:16.337Z [ERROR] ⚠️  High memory usage: 91.26%
2025-07-03T18:27:16.338Z [ERROR] ⚠️  High memory usage: 92.57%
2025-07-03T18:47:16.339Z [ERROR] ⚠️  High memory usage: 90.69%
2025-07-03T18:52:16.339Z [ERROR] ⚠️  High memory usage: 92.33%
2025-07-03T19:12:16.342Z [ERROR] ⚠️  High memory usage: 90.75%
2025-07-03T19:17:16.343Z [ERROR] ⚠️  High memory usage: 91.07%
2025-07-03T19:42:16.346Z [ERROR] ⚠️  High memory usage: 90.79%
2025-07-03T19:47:16.346Z [ERROR] ⚠️  High memory usage: 91.09%
2025-07-03T19:47:25.101Z [ERROR] Error fetching current UTC time: getaddrinfo EAI_AGAIN timeapi.io
2025-07-03T20:07:16.349Z [ERROR] ⚠️  High memory usage: 90.08%
2025-07-03T20:12:16.350Z [ERROR] ⚠️  High memory usage: 90.99%
2025-07-03T20:32:16.352Z [ERROR] ⚠️  High memory usage: 90.82%
2025-07-03T20:37:16.351Z [ERROR] ⚠️  High memory usage: 92.03%
2025-07-03T20:42:16.352Z [ERROR] ⚠️  High memory usage: 92.90%
2025-07-03T21:02:16.354Z [ERROR] ⚠️  High memory usage: 90.37%
2025-07-03T21:07:16.355Z [ERROR] ⚠️  High memory usage: 91.33%
2025-07-03T21:12:16.356Z [ERROR] ⚠️  High memory usage: 92.06%
2025-07-03T21:17:16.355Z [ERROR] ⚠️  High memory usage: 92.16%
2025-07-03T21:22:16.355Z [ERROR] ⚠️  High memory usage: 92.95%
2025-07-03T21:27:16.355Z [ERROR] ⚠️  High memory usage: 92.77%
2025-07-03T21:32:16.355Z [ERROR] ⚠️  High memory usage: 93.63%
2025-07-03T21:57:16.358Z [ERROR] ⚠️  High memory usage: 90.58%
2025-07-03T22:07:16.358Z [ERROR] ⚠️  High memory usage: 93.78%
2025-07-03T22:22:16.359Z [ERROR] ⚠️  High memory usage: 90.07%
2025-07-03T22:27:16.359Z [ERROR] ⚠️  High memory usage: 90.59%
2025-07-03T22:32:16.359Z [ERROR] ⚠️  High memory usage: 91.74%
2025-07-03T22:37:16.360Z [ERROR] ⚠️  High memory usage: 92.76%
2025-07-03T22:47:16.360Z [ERROR] ⚠️  High memory usage: 90.33%
2025-07-03T22:52:16.360Z [ERROR] ⚠️  High memory usage: 91.24%
2025-07-03T22:57:16.361Z [ERROR] ⚠️  High memory usage: 92.56%
2025-07-03T23:02:16.361Z [ERROR] ⚠️  High memory usage: 93.73%
2025-07-03T23:17:16.364Z [ERROR] ⚠️  High memory usage: 90.28%
2025-07-03T23:22:16.364Z [ERROR] ⚠️  High memory usage: 91.40%
2025-07-03T23:27:16.365Z [ERROR] ⚠️  High memory usage: 93.61%
2025-07-03T23:32:16.365Z [ERROR] ⚠️  High memory usage: 94.38%
2025-07-03T23:47:16.367Z [ERROR] ⚠️  High memory usage: 90.69%
2025-07-03T23:52:16.367Z [ERROR] ⚠️  High memory usage: 91.29%
2025-07-03T23:57:16.367Z [ERROR] ⚠️  High memory usage: 92.41%
2025-07-04T00:12:16.369Z [ERROR] ⚠️  High memory usage: 90.70%
2025-07-04T00:17:16.370Z [ERROR] ⚠️  High memory usage: 91.98%
2025-07-04T00:22:16.371Z [ERROR] ⚠️  High memory usage: 92.97%
2025-07-04T00:27:16.372Z [ERROR] ⚠️  High memory usage: 94.15%
2025-07-04T00:32:16.372Z [ERROR] ⚠️  High memory usage: 93.39%
2025-07-04T00:37:16.373Z [ERROR] ⚠️  High memory usage: 92.45%
2025-07-04T00:42:16.374Z [ERROR] ⚠️  High memory usage: 91.24%
2025-07-04T00:57:16.373Z [ERROR] ⚠️  High memory usage: 91.43%
2025-07-04T01:12:16.373Z [ERROR] ⚠️  High memory usage: 92.72%
2025-07-04T01:22:16.373Z [ERROR] ⚠️  High memory usage: 91.73%
2025-07-04T01:37:16.375Z [ERROR] ⚠️  High memory usage: 93.11%
2025-07-04T01:47:16.374Z [ERROR] ⚠️  High memory usage: 91.59%
2025-07-04T01:57:16.375Z [ERROR] ⚠️  High memory usage: 90.12%
2025-07-04T02:02:16.375Z [ERROR] ⚠️  High memory usage: 92.82%
2025-07-04T02:22:16.376Z [ERROR] ⚠️  High memory usage: 91.17%
2025-07-04T02:27:16.376Z [ERROR] ⚠️  High memory usage: 92.01%
2025-07-04T03:12:16.379Z [ERROR] ⚠️  High memory usage: 90.57%
2025-07-04T03:17:16.379Z [ERROR] ⚠️  High memory usage: 91.12%
2025-07-04T03:42:16.379Z [ERROR] ⚠️  High memory usage: 90.74%
2025-07-04T04:12:16.383Z [ERROR] ⚠️  High memory usage: 90.95%
2025-07-04T04:37:16.385Z [ERROR] ⚠️  High memory usage: 90.92%
2025-07-04T05:07:16.387Z [ERROR] ⚠️  High memory usage: 90.49%
2025-07-04T05:12:16.387Z [ERROR] ⚠️  High memory usage: 91.56%
2025-07-04T05:17:16.388Z [ERROR] ⚠️  High memory usage: 92.45%
2025-07-04T05:24:18.223Z [ERROR] [FFMPEG_STDERR] ae8f8bdd-f37f-4a62-b6eb-760571bc86a2: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/sqew-1dvz-e9gr-adg4-2sqm: Broken pipe
2025-07-04T05:24:18.250Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream ae8f8bdd-f37f-4a62-b6eb-760571bc86a2
2025-07-04T05:37:16.390Z [ERROR] ⚠️  High memory usage: 91.03%
2025-07-04T05:42:16.390Z [ERROR] ⚠️  High memory usage: 91.32%
2025-07-04T06:02:16.393Z [ERROR] ⚠️  High memory usage: 90.49%
2025-07-04T06:07:16.393Z [ERROR] ⚠️  High memory usage: 91.50%
2025-07-04T06:27:16.394Z [ERROR] ⚠️  High memory usage: 90.08%
2025-07-04T06:32:16.393Z [ERROR] ⚠️  High memory usage: 90.01%
2025-07-04T06:37:16.393Z [ERROR] ⚠️  High memory usage: 91.52%
2025-07-04T06:57:16.395Z [ERROR] ⚠️  High memory usage: 90.15%
2025-07-04T07:02:16.395Z [ERROR] ⚠️  High memory usage: 90.89%
2025-07-04T07:27:16.398Z [ERROR] ⚠️  High memory usage: 90.91%
2025-07-04T07:32:16.399Z [ERROR] ⚠️  High memory usage: 91.03%
2025-07-04T07:57:16.401Z [ERROR] ⚠️  High memory usage: 90.97%
2025-07-04T08:22:16.401Z [ERROR] ⚠️  High memory usage: 90.32%
2025-07-04T08:27:16.400Z [ERROR] ⚠️  High memory usage: 91.77%
2025-07-04T08:37:16.401Z [ERROR] ⚠️  High memory usage: 90.46%
2025-07-04T08:42:16.401Z [ERROR] ⚠️  High memory usage: 91.48%
2025-07-04T09:07:16.405Z [ERROR] ⚠️  High memory usage: 90.66%
2025-07-04T09:12:16.405Z [ERROR] ⚠️  High memory usage: 90.62%
2025-07-04T09:32:16.407Z [ERROR] ⚠️  High memory usage: 90.42%
2025-07-04T09:37:16.407Z [ERROR] ⚠️  High memory usage: 90.66%
2025-07-04T10:02:16.409Z [ERROR] ⚠️  High memory usage: 90.05%
2025-07-04T10:07:16.410Z [ERROR] ⚠️  High memory usage: 91.62%
2025-07-04T10:32:16.412Z [ERROR] ⚠️  High memory usage: 90.75%
2025-07-04T10:37:16.412Z [ERROR] ⚠️  High memory usage: 91.71%
2025-07-04T10:47:16.413Z [ERROR] ⚠️  High memory usage: 92.29%
2025-07-04T10:57:16.415Z [ERROR] ⚠️  High memory usage: 91.41%
2025-07-04T11:07:16.415Z [ERROR] ⚠️  High memory usage: 91.21%
2025-07-04T11:17:16.416Z [ERROR] ⚠️  High memory usage: 92.13%
2025-07-04T11:32:16.417Z [ERROR] ⚠️  High memory usage: 93.12%
2025-07-04T11:37:16.417Z [ERROR] ⚠️  High memory usage: 94.60%
2025-07-04T11:42:16.418Z [ERROR] ⚠️  High memory usage: 93.91%
2025-07-04T11:47:16.418Z [ERROR] ⚠️  High memory usage: 90.17%
2025-07-04T11:57:16.418Z [ERROR] ⚠️  High memory usage: 92.36%
2025-07-04T12:07:16.419Z [ERROR] ⚠️  High memory usage: 93.28%
2025-07-04T12:12:16.419Z [ERROR] ⚠️  High memory usage: 90.28%
2025-07-04T12:22:16.419Z [ERROR] ⚠️  High memory usage: 91.56%
2025-07-04T12:32:16.421Z [ERROR] ⚠️  High memory usage: 93.04%
2025-07-04T12:37:16.420Z [ERROR] ⚠️  High memory usage: 90.40%
2025-07-04T12:47:16.421Z [ERROR] ⚠️  High memory usage: 91.41%
2025-07-04T13:02:16.422Z [ERROR] ⚠️  High memory usage: 90.00%
2025-07-04T13:12:16.424Z [ERROR] ⚠️  High memory usage: 92.23%
2025-07-04T13:17:16.424Z [ERROR] ⚠️  High memory usage: 90.20%
2025-07-04T13:27:16.425Z [ERROR] ⚠️  High memory usage: 92.67%
2025-07-04T13:33:49.580Z [ERROR] [FFMPEG_STDERR] ae8f8bdd-f37f-4a62-b6eb-760571bc86a2: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/sqew-1dvz-e9gr-adg4-2sqm: Connection reset by peer
2025-07-04T13:33:49.601Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream ae8f8bdd-f37f-4a62-b6eb-760571bc86a2
2025-07-04T13:42:16.427Z [ERROR] ⚠️  High memory usage: 91.16%
2025-07-04T13:52:16.427Z [ERROR] ⚠️  High memory usage: 93.14%
2025-07-04T14:02:16.427Z [ERROR] ⚠️  High memory usage: 92.26%
2025-07-04T14:12:16.427Z [ERROR] ⚠️  High memory usage: 90.34%
2025-07-04T14:37:16.427Z [ERROR] ⚠️  High memory usage: 91.43%
2025-07-04T15:02:16.429Z [ERROR] ⚠️  High memory usage: 91.39%
2025-07-04T15:57:16.431Z [ERROR] ⚠️  High memory usage: 90.73%
2025-07-04T16:22:16.435Z [ERROR] ⚠️  High memory usage: 90.08%
2025-07-04T16:52:16.436Z [ERROR] ⚠️  High memory usage: 90.56%
2025-07-04T18:17:16.443Z [ERROR] ⚠️  High memory usage: 90.15%
2025-07-04T18:47:16.445Z [ERROR] ⚠️  High memory usage: 90.81%
2025-07-04T19:12:16.446Z [ERROR] ⚠️  High memory usage: 90.13%
2025-07-04T19:47:16.450Z [ERROR] ⚠️  High memory usage: 91.25%
2025-07-04T20:12:16.451Z [ERROR] ⚠️  High memory usage: 90.41%
2025-07-04T21:12:16.455Z [ERROR] ⚠️  High memory usage: 90.51%
2025-07-04T21:24:23.718Z [ERROR] [FFMPEG_STDERR] ba5857c7-1ea6-4464-b60a-440ba8b877fc: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/murk-epb3-1gjj-v5df-9h70: Connection reset by peer
2025-07-04T21:24:23.743Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream ba5857c7-1ea6-4464-b60a-440ba8b877fc
2025-07-04T21:42:16.457Z [ERROR] ⚠️  High memory usage: 90.86%
2025-07-04T22:42:16.461Z [ERROR] ⚠️  High memory usage: 90.30%
2025-07-04T22:47:16.462Z [ERROR] ⚠️  High memory usage: 90.68%
2025-07-04T22:52:16.462Z [ERROR] ⚠️  High memory usage: 91.49%
2025-07-04T23:17:16.462Z [ERROR] ⚠️  High memory usage: 90.17%
2025-07-04T23:42:16.463Z [ERROR] ⚠️  High memory usage: 90.37%
2025-07-04T23:47:16.464Z [ERROR] ⚠️  High memory usage: 90.87%
2025-07-05T00:12:16.465Z [ERROR] ⚠️  High memory usage: 90.31%
2025-07-05T00:17:16.465Z [ERROR] ⚠️  High memory usage: 91.44%
2025-07-05T00:37:16.467Z [ERROR] ⚠️  High memory usage: 90.45%
2025-07-05T00:42:16.467Z [ERROR] ⚠️  High memory usage: 91.26%
2025-07-05T01:12:16.471Z [ERROR] ⚠️  High memory usage: 91.33%
2025-07-05T01:37:16.472Z [ERROR] ⚠️  High memory usage: 90.70%
2025-07-05T02:07:16.473Z [ERROR] ⚠️  High memory usage: 90.83%
2025-07-05T02:32:16.473Z [ERROR] ⚠️  High memory usage: 90.35%
2025-07-05T02:37:16.473Z [ERROR] ⚠️  High memory usage: 90.45%
2025-07-05T03:02:16.475Z [ERROR] ⚠️  High memory usage: 91.11%
2025-07-05T03:27:16.478Z [ERROR] ⚠️  High memory usage: 92.22%
2025-07-05T03:47:16.478Z [ERROR] ⚠️  High memory usage: 90.28%
2025-07-05T03:52:16.479Z [ERROR] ⚠️  High memory usage: 91.46%
2025-07-05T04:17:16.480Z [ERROR] ⚠️  High memory usage: 90.61%
2025-07-05T04:22:16.481Z [ERROR] ⚠️  High memory usage: 91.13%
2025-07-05T04:42:16.483Z [ERROR] ⚠️  High memory usage: 90.05%
2025-07-05T04:47:16.482Z [ERROR] ⚠️  High memory usage: 91.10%
2025-07-05T05:12:16.483Z [ERROR] ⚠️  High memory usage: 90.63%
2025-07-05T05:17:16.483Z [ERROR] ⚠️  High memory usage: 91.18%
2025-07-05T05:42:16.484Z [ERROR] ⚠️  High memory usage: 90.37%
2025-07-05T06:37:16.488Z [ERROR] ⚠️  High memory usage: 90.50%
2025-07-05T07:02:16.489Z [ERROR] ⚠️  High memory usage: 90.79%
2025-07-05T07:27:16.492Z [ERROR] ⚠️  High memory usage: 90.49%
2025-07-05T07:32:16.493Z [ERROR] ⚠️  High memory usage: 91.49%
2025-07-05T07:57:16.495Z [ERROR] ⚠️  High memory usage: 90.76%
2025-07-05T08:22:16.498Z [ERROR] ⚠️  High memory usage: 90.21%
2025-07-05T08:27:16.498Z [ERROR] ⚠️  High memory usage: 91.45%
2025-07-05T08:52:16.499Z [ERROR] ⚠️  High memory usage: 90.60%
2025-07-05T09:22:16.503Z [ERROR] ⚠️  High memory usage: 91.73%
2025-07-05T09:47:16.506Z [ERROR] ⚠️  High memory usage: 90.84%
2025-07-05T10:42:16.510Z [ERROR] ⚠️  High memory usage: 90.39%
2025-07-05T11:12:16.512Z [ERROR] ⚠️  High memory usage: 90.42%
2025-07-05T11:32:16.514Z [ERROR] ⚠️  High memory usage: 90.12%
2025-07-05T11:37:16.515Z [ERROR] ⚠️  High memory usage: 91.92%
2025-07-05T11:42:16.515Z [ERROR] ⚠️  High memory usage: 93.65%
2025-07-05T11:47:16.516Z [ERROR] ⚠️  High memory usage: 93.94%
2025-07-05T11:52:16.517Z [ERROR] ⚠️  High memory usage: 94.26%
2025-07-05T11:57:16.517Z [ERROR] ⚠️  High memory usage: 91.63%
2025-07-05T12:02:16.517Z [ERROR] ⚠️  High memory usage: 93.91%
2025-07-05T12:07:16.517Z [ERROR] ⚠️  High memory usage: 91.77%
2025-07-05T12:12:16.518Z [ERROR] ⚠️  High memory usage: 93.71%
2025-07-05T12:22:16.518Z [ERROR] ⚠️  High memory usage: 94.04%
2025-07-05T12:27:16.519Z [ERROR] ⚠️  High memory usage: 93.80%
2025-07-05T12:32:16.519Z [ERROR] ⚠️  High memory usage: 92.34%
2025-07-05T12:37:16.520Z [ERROR] ⚠️  High memory usage: 91.01%
2025-07-05T12:42:16.519Z [ERROR] ⚠️  High memory usage: 93.62%
2025-07-05T12:47:16.519Z [ERROR] ⚠️  High memory usage: 93.76%
2025-07-05T12:57:16.520Z [ERROR] ⚠️  High memory usage: 92.66%
2025-07-05T13:02:16.521Z [ERROR] ⚠️  High memory usage: 90.29%
2025-07-05T13:07:16.521Z [ERROR] ⚠️  High memory usage: 93.14%
2025-07-05T13:12:16.522Z [ERROR] ⚠️  High memory usage: 93.06%
2025-07-05T13:27:16.524Z [ERROR] ⚠️  High memory usage: 90.49%
2025-07-05T13:32:16.524Z [ERROR] ⚠️  High memory usage: 91.29%
2025-07-05T13:37:16.524Z [ERROR] ⚠️  High memory usage: 91.80%
2025-07-05T13:57:16.527Z [ERROR] ⚠️  High memory usage: 90.56%
2025-07-05T14:02:16.527Z [ERROR] ⚠️  High memory usage: 91.16%
2025-07-05T14:07:16.527Z [ERROR] ⚠️  High memory usage: 92.38%
2025-07-05T14:27:16.531Z [ERROR] ⚠️  High memory usage: 91.08%
2025-07-05T14:32:16.531Z [ERROR] ⚠️  High memory usage: 91.87%
2025-07-05T14:37:16.532Z [ERROR] ⚠️  High memory usage: 92.80%
2025-07-05T14:52:16.533Z [ERROR] ⚠️  High memory usage: 90.08%
2025-07-05T14:57:16.534Z [ERROR] ⚠️  High memory usage: 90.93%
2025-07-05T15:00:07.136Z [ERROR] [FFMPEG_STDERR] ba5857c7-1ea6-4464-b60a-440ba8b877fc: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/murk-epb3-1gjj-v5df-9h70: Broken pipe
2025-07-05T15:00:07.162Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream ba5857c7-1ea6-4464-b60a-440ba8b877fc
2025-07-05T15:17:16.534Z [ERROR] ⚠️  High memory usage: 90.05%
2025-07-05T15:22:16.534Z [ERROR] ⚠️  High memory usage: 90.61%
2025-07-05T15:27:16.534Z [ERROR] ⚠️  High memory usage: 91.76%
2025-07-05T15:47:16.547Z [ERROR] ⚠️  High memory usage: 90.74%
2025-07-05T15:52:16.548Z [ERROR] ⚠️  High memory usage: 91.48%
2025-07-05T15:57:16.548Z [ERROR] ⚠️  High memory usage: 92.40%
2025-07-05T16:17:16.550Z [ERROR] ⚠️  High memory usage: 90.88%
2025-07-05T16:22:16.551Z [ERROR] ⚠️  High memory usage: 91.97%
2025-07-05T16:42:16.552Z [ERROR] ⚠️  High memory usage: 90.22%
2025-07-05T16:47:16.552Z [ERROR] ⚠️  High memory usage: 91.18%
2025-07-05T16:52:16.552Z [ERROR] ⚠️  High memory usage: 92.35%
2025-07-05T17:12:16.554Z [ERROR] ⚠️  High memory usage: 90.69%
2025-07-05T17:17:16.554Z [ERROR] ⚠️  High memory usage: 91.84%
2025-07-05T17:22:16.562Z [ERROR] ⚠️  High memory usage: 92.82%
2025-07-05T17:42:16.564Z [ERROR] ⚠️  High memory usage: 90.60%
2025-07-05T17:47:16.564Z [ERROR] ⚠️  High memory usage: 91.61%
2025-07-05T17:52:16.564Z [ERROR] ⚠️  High memory usage: 93.21%
2025-07-05T18:07:16.565Z [ERROR] ⚠️  High memory usage: 90.21%
2025-07-05T18:12:16.565Z [ERROR] ⚠️  High memory usage: 90.41%
2025-07-05T18:17:16.565Z [ERROR] ⚠️  High memory usage: 91.60%
2025-07-05T18:37:16.565Z [ERROR] ⚠️  High memory usage: 90.45%
2025-07-05T18:42:16.565Z [ERROR] ⚠️  High memory usage: 91.26%
2025-07-05T18:47:16.566Z [ERROR] ⚠️  High memory usage: 92.09%
2025-07-05T19:07:16.569Z [ERROR] ⚠️  High memory usage: 90.74%
2025-07-05T19:12:16.570Z [ERROR] ⚠️  High memory usage: 91.23%
2025-07-05T19:17:16.571Z [ERROR] ⚠️  High memory usage: 92.38%
2025-07-05T19:37:16.573Z [ERROR] ⚠️  High memory usage: 90.55%
2025-07-05T19:42:16.574Z [ERROR] ⚠️  High memory usage: 91.78%
2025-07-05T19:47:16.574Z [ERROR] ⚠️  High memory usage: 92.78%
2025-07-05T20:07:16.580Z [ERROR] ⚠️  High memory usage: 91.30%
2025-07-05T20:12:16.579Z [ERROR] ⚠️  High memory usage: 92.17%
2025-07-05T20:17:16.579Z [ERROR] ⚠️  High memory usage: 92.35%
2025-07-05T20:32:16.579Z [ERROR] ⚠️  High memory usage: 90.32%
2025-07-05T20:37:16.580Z [ERROR] ⚠️  High memory usage: 91.30%
2025-07-05T20:42:16.580Z [ERROR] ⚠️  High memory usage: 92.22%
2025-07-05T21:02:16.581Z [ERROR] ⚠️  High memory usage: 90.42%
2025-07-05T21:07:16.582Z [ERROR] ⚠️  High memory usage: 91.67%
2025-07-05T21:12:16.583Z [ERROR] ⚠️  High memory usage: 92.41%
2025-07-05T21:27:16.584Z [ERROR] ⚠️  High memory usage: 90.01%
2025-07-05T21:32:16.585Z [ERROR] ⚠️  High memory usage: 91.20%
2025-07-05T21:37:16.584Z [ERROR] ⚠️  High memory usage: 91.73%
2025-07-05T21:42:16.584Z [ERROR] ⚠️  High memory usage: 92.79%
2025-07-05T21:57:16.585Z [ERROR] ⚠️  High memory usage: 90.08%
2025-07-05T22:02:16.585Z [ERROR] ⚠️  High memory usage: 90.39%
2025-07-05T22:07:16.585Z [ERROR] ⚠️  High memory usage: 91.35%
2025-07-05T22:12:16.586Z [ERROR] ⚠️  High memory usage: 92.63%
2025-07-05T22:27:16.587Z [ERROR] ⚠️  High memory usage: 90.06%
2025-07-05T22:32:16.588Z [ERROR] ⚠️  High memory usage: 90.99%
2025-07-05T22:37:16.588Z [ERROR] ⚠️  High memory usage: 92.51%
2025-07-05T22:57:16.591Z [ERROR] ⚠️  High memory usage: 90.33%
2025-07-05T23:02:16.591Z [ERROR] ⚠️  High memory usage: 91.01%
2025-07-05T23:07:16.592Z [ERROR] ⚠️  High memory usage: 92.56%
2025-07-05T23:27:16.593Z [ERROR] ⚠️  High memory usage: 91.15%
2025-07-05T23:32:16.593Z [ERROR] ⚠️  High memory usage: 91.83%
2025-07-05T23:37:16.593Z [ERROR] ⚠️  High memory usage: 92.92%
2025-07-05T23:57:16.593Z [ERROR] ⚠️  High memory usage: 91.07%
2025-07-06T00:02:16.593Z [ERROR] ⚠️  High memory usage: 91.98%
2025-07-06T00:22:16.594Z [ERROR] ⚠️  High memory usage: 90.41%
2025-07-06T00:27:16.595Z [ERROR] ⚠️  High memory usage: 91.12%
2025-07-06T00:32:16.595Z [ERROR] ⚠️  High memory usage: 92.61%
2025-07-06T00:52:16.597Z [ERROR] ⚠️  High memory usage: 90.89%
2025-07-06T00:57:16.598Z [ERROR] ⚠️  High memory usage: 90.56%
2025-07-06T01:02:16.599Z [ERROR] ⚠️  High memory usage: 91.98%
2025-07-06T01:22:16.600Z [ERROR] ⚠️  High memory usage: 90.93%
2025-07-06T01:27:16.600Z [ERROR] ⚠️  High memory usage: 91.90%
2025-07-06T01:42:16.601Z [ERROR] ⚠️  High memory usage: 91.36%
2025-07-06T01:52:16.602Z [ERROR] ⚠️  High memory usage: 91.70%
2025-07-06T02:02:16.602Z [ERROR] ⚠️  High memory usage: 90.15%
2025-07-06T02:07:16.603Z [ERROR] ⚠️  High memory usage: 92.68%
2025-07-06T02:12:16.604Z [ERROR] ⚠️  High memory usage: 93.23%
2025-07-06T02:17:16.604Z [ERROR] ⚠️  High memory usage: 94.03%
2025-07-06T02:22:16.604Z [ERROR] ⚠️  High memory usage: 94.18%
2025-07-06T02:37:16.605Z [ERROR] ⚠️  High memory usage: 90.53%
2025-07-06T02:42:16.605Z [ERROR] ⚠️  High memory usage: 91.75%
2025-07-06T02:47:16.606Z [ERROR] ⚠️  High memory usage: 92.02%
2025-07-06T02:52:16.607Z [ERROR] ⚠️  High memory usage: 93.67%
2025-07-06T03:02:16.607Z [ERROR] ⚠️  High memory usage: 90.68%
2025-07-06T03:07:16.607Z [ERROR] ⚠️  High memory usage: 91.66%
2025-07-06T03:12:16.608Z [ERROR] ⚠️  High memory usage: 92.45%
2025-07-06T03:22:16.608Z [ERROR] ⚠️  High memory usage: 90.70%
2025-07-06T03:27:16.609Z [ERROR] ⚠️  High memory usage: 90.48%
2025-07-06T03:32:16.609Z [ERROR] ⚠️  High memory usage: 91.47%
2025-07-06T03:37:16.609Z [ERROR] ⚠️  High memory usage: 90.65%
2025-07-06T03:42:16.609Z [ERROR] ⚠️  High memory usage: 91.28%
2025-07-06T03:47:16.609Z [ERROR] ⚠️  High memory usage: 90.68%
2025-07-06T03:52:16.609Z [ERROR] ⚠️  High memory usage: 91.01%
2025-07-06T03:57:16.611Z [ERROR] ⚠️  High memory usage: 90.47%
2025-07-06T04:02:16.612Z [ERROR] ⚠️  High memory usage: 91.36%
2025-07-06T04:07:16.612Z [ERROR] ⚠️  High memory usage: 91.15%
2025-07-06T04:12:16.612Z [ERROR] ⚠️  High memory usage: 90.57%
2025-07-06T04:17:16.613Z [ERROR] ⚠️  High memory usage: 92.28%
2025-07-06T04:22:16.614Z [ERROR] ⚠️  High memory usage: 93.09%
2025-07-06T04:27:16.615Z [ERROR] ⚠️  High memory usage: 93.51%
2025-07-06T04:32:16.615Z [ERROR] ⚠️  High memory usage: 90.96%
2025-07-06T04:37:16.615Z [ERROR] ⚠️  High memory usage: 91.47%
2025-07-06T04:42:16.615Z [ERROR] ⚠️  High memory usage: 90.49%
2025-07-06T04:47:16.615Z [ERROR] ⚠️  High memory usage: 91.47%
2025-07-06T04:52:16.616Z [ERROR] ⚠️  High memory usage: 91.33%
2025-07-06T04:57:16.616Z [ERROR] ⚠️  High memory usage: 91.66%
2025-07-06T05:02:16.617Z [ERROR] ⚠️  High memory usage: 91.85%
2025-07-06T05:12:16.619Z [ERROR] ⚠️  High memory usage: 91.96%
2025-07-06T05:17:16.619Z [ERROR] ⚠️  High memory usage: 92.81%
2025-07-06T05:22:16.619Z [ERROR] ⚠️  High memory usage: 92.61%
2025-07-06T05:27:16.620Z [ERROR] ⚠️  High memory usage: 92.46%
2025-07-06T05:32:16.620Z [ERROR] ⚠️  High memory usage: 94.50%
2025-07-06T05:42:16.620Z [ERROR] ⚠️  High memory usage: 93.18%
2025-07-06T05:52:16.621Z [ERROR] ⚠️  High memory usage: 92.52%
2025-07-06T06:02:16.622Z [ERROR] ⚠️  High memory usage: 90.65%
2025-07-06T06:07:16.622Z [ERROR] ⚠️  High memory usage: 91.19%
2025-07-06T06:12:16.622Z [ERROR] ⚠️  High memory usage: 91.82%
2025-07-06T06:32:16.625Z [ERROR] ⚠️  High memory usage: 90.31%
2025-07-06T06:37:16.626Z [ERROR] ⚠️  High memory usage: 90.99%
2025-07-06T06:42:16.626Z [ERROR] ⚠️  High memory usage: 91.98%
2025-07-06T07:07:16.628Z [ERROR] ⚠️  High memory usage: 91.26%
2025-07-06T07:17:16.630Z [ERROR] ⚠️  High memory usage: 92.99%
2025-07-06T07:47:16.636Z [ERROR] ⚠️  High memory usage: 90.32%
2025-07-06T07:57:16.637Z [ERROR] ⚠️  High memory usage: 90.51%
2025-07-06T08:07:16.639Z [ERROR] ⚠️  High memory usage: 92.89%
2025-07-06T08:17:16.640Z [ERROR] ⚠️  High memory usage: 93.96%
2025-07-06T08:22:16.640Z [ERROR] ⚠️  High memory usage: 95.78%
2025-07-06T08:32:16.641Z [ERROR] ⚠️  High memory usage: 92.07%
2025-07-06T08:37:16.642Z [ERROR] ⚠️  High memory usage: 94.67%
2025-07-06T08:47:16.643Z [ERROR] ⚠️  High memory usage: 93.72%
2025-07-06T09:02:16.645Z [ERROR] ⚠️  High memory usage: 90.29%
2025-07-06T09:07:16.645Z [ERROR] ⚠️  High memory usage: 91.74%
2025-07-06T09:12:16.645Z [ERROR] ⚠️  High memory usage: 92.62%
2025-07-06T09:17:16.645Z [ERROR] ⚠️  High memory usage: 93.29%
2025-07-06T09:27:16.659Z [ERROR] ⚠️  High memory usage: 90.04%
2025-07-06T09:32:16.660Z [ERROR] ⚠️  High memory usage: 90.50%
2025-07-06T09:37:16.660Z [ERROR] ⚠️  High memory usage: 91.61%
2025-07-06T09:42:16.660Z [ERROR] ⚠️  High memory usage: 92.81%
2025-07-06T09:47:16.660Z [ERROR] ⚠️  High memory usage: 93.64%
2025-07-06T09:57:16.660Z [ERROR] ⚠️  High memory usage: 90.49%
2025-07-06T10:02:16.660Z [ERROR] ⚠️  High memory usage: 91.51%
2025-07-06T10:07:16.661Z [ERROR] ⚠️  High memory usage: 92.65%
2025-07-06T10:12:16.662Z [ERROR] ⚠️  High memory usage: 93.06%
2025-07-06T10:17:16.662Z [ERROR] ⚠️  High memory usage: 92.99%
2025-07-06T10:22:16.662Z [ERROR] ⚠️  High memory usage: 92.88%
2025-07-06T10:27:16.663Z [ERROR] ⚠️  High memory usage: 92.64%
2025-07-06T10:32:16.663Z [ERROR] ⚠️  High memory usage: 93.20%
2025-07-06T10:37:16.663Z [ERROR] ⚠️  High memory usage: 93.08%
2025-07-06T10:42:16.663Z [ERROR] ⚠️  High memory usage: 93.62%
2025-07-06T10:47:16.664Z [ERROR] ⚠️  High memory usage: 93.82%
2025-07-06T10:52:16.665Z [ERROR] ⚠️  High memory usage: 94.26%
2025-07-06T10:57:16.665Z [ERROR] ⚠️  High memory usage: 94.95%
2025-07-06T11:02:16.665Z [ERROR] ⚠️  High memory usage: 95.06%
2025-07-06T11:07:16.665Z [ERROR] ⚠️  High memory usage: 95.10%
2025-07-06T11:12:16.665Z [ERROR] ⚠️  High memory usage: 90.48%
2025-07-06T11:17:16.665Z [ERROR] ⚠️  High memory usage: 92.46%
2025-07-06T11:27:16.666Z [ERROR] ⚠️  High memory usage: 90.84%
2025-07-06T11:32:16.667Z [ERROR] ⚠️  High memory usage: 94.49%
2025-07-06T11:42:16.668Z [ERROR] ⚠️  High memory usage: 92.54%
2025-07-06T11:52:16.668Z [ERROR] ⚠️  High memory usage: 90.75%
2025-07-06T11:57:16.668Z [ERROR] ⚠️  High memory usage: 93.09%
2025-07-06T12:07:16.669Z [ERROR] ⚠️  High memory usage: 91.55%
2025-07-06T12:22:16.669Z [ERROR] ⚠️  High memory usage: 93.40%
2025-07-06T12:32:16.670Z [ERROR] ⚠️  High memory usage: 90.04%
2025-07-06T12:37:16.671Z [ERROR] ⚠️  High memory usage: 90.81%
2025-07-06T12:42:16.672Z [ERROR] ⚠️  High memory usage: 91.83%
2025-07-06T12:47:16.672Z [ERROR] ⚠️  High memory usage: 92.18%
2025-07-06T13:12:16.675Z [ERROR] ⚠️  High memory usage: 91.60%
2025-07-06T13:17:16.676Z [ERROR] ⚠️  High memory usage: 92.11%
2025-07-06T13:32:16.676Z [ERROR] ⚠️  High memory usage: 90.17%
2025-07-06T13:37:16.677Z [ERROR] ⚠️  High memory usage: 91.78%
2025-07-06T13:42:16.678Z [ERROR] ⚠️  High memory usage: 92.73%
2025-07-06T14:02:16.680Z [ERROR] ⚠️  High memory usage: 90.76%
2025-07-06T14:07:16.681Z [ERROR] ⚠️  High memory usage: 91.85%
2025-07-06T14:12:16.681Z [ERROR] ⚠️  High memory usage: 91.98%
2025-07-06T14:27:16.683Z [ERROR] ⚠️  High memory usage: 90.52%
2025-07-06T14:32:16.682Z [ERROR] ⚠️  High memory usage: 90.94%
2025-07-06T14:37:16.682Z [ERROR] ⚠️  High memory usage: 92.03%
2025-07-06T14:57:16.685Z [ERROR] ⚠️  High memory usage: 90.74%
2025-07-06T15:02:16.686Z [ERROR] ⚠️  High memory usage: 91.56%
2025-07-06T15:07:16.687Z [ERROR] ⚠️  High memory usage: 92.21%
2025-07-06T15:27:16.688Z [ERROR] ⚠️  High memory usage: 90.43%
2025-07-06T15:32:16.688Z [ERROR] ⚠️  High memory usage: 91.75%
2025-07-06T15:37:16.688Z [ERROR] ⚠️  High memory usage: 92.62%
2025-07-06T15:52:16.689Z [ERROR] ⚠️  High memory usage: 90.03%
2025-07-06T15:57:12.764Z [ERROR] [FFMPEG_STDERR] ba5857c7-1ea6-4464-b60a-440ba8b877fc: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/murk-epb3-1gjj-v5df-9h70: Broken pipe
2025-07-06T15:57:13.509Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream ba5857c7-1ea6-4464-b60a-440ba8b877fc
2025-07-06T15:57:16.690Z [ERROR] ⚠️  High memory usage: 90.74%
2025-07-06T16:02:16.690Z [ERROR] ⚠️  High memory usage: 91.54%
2025-07-06T16:22:16.692Z [ERROR] ⚠️  High memory usage: 90.37%
2025-07-06T16:27:16.693Z [ERROR] ⚠️  High memory usage: 91.22%
2025-07-06T16:32:16.694Z [ERROR] ⚠️  High memory usage: 92.15%
2025-07-06T16:47:16.695Z [ERROR] ⚠️  High memory usage: 90.65%
2025-07-06T16:52:16.695Z [ERROR] ⚠️  High memory usage: 92.66%
2025-07-06T17:17:16.697Z [ERROR] ⚠️  High memory usage: 90.94%
2025-07-06T17:42:16.701Z [ERROR] ⚠️  High memory usage: 91.11%
2025-07-06T17:47:16.701Z [ERROR] ⚠️  High memory usage: 91.94%
2025-07-06T18:02:16.704Z [ERROR] ⚠️  High memory usage: 90.26%
2025-07-06T18:07:16.705Z [ERROR] ⚠️  High memory usage: 91.33%
2025-07-06T18:12:16.706Z [ERROR] ⚠️  High memory usage: 92.58%
2025-07-06T18:27:16.709Z [ERROR] ⚠️  High memory usage: 90.23%
2025-07-06T18:32:16.710Z [ERROR] ⚠️  High memory usage: 91.14%
2025-07-06T18:37:16.710Z [ERROR] ⚠️  High memory usage: 92.00%
2025-07-06T18:42:16.711Z [ERROR] ⚠️  High memory usage: 92.84%
2025-07-06T18:57:16.712Z [ERROR] ⚠️  High memory usage: 90.83%
2025-07-06T19:02:16.715Z [ERROR] ⚠️  High memory usage: 91.52%
2025-07-06T19:22:16.716Z [ERROR] ⚠️  High memory usage: 90.59%
2025-07-06T19:27:16.717Z [ERROR] ⚠️  High memory usage: 91.72%
2025-07-06T19:32:16.717Z [ERROR] ⚠️  High memory usage: 92.33%
2025-07-06T19:52:16.719Z [ERROR] ⚠️  High memory usage: 90.13%
2025-07-06T19:57:16.718Z [ERROR] ⚠️  High memory usage: 91.00%
2025-07-06T20:02:16.718Z [ERROR] ⚠️  High memory usage: 92.17%
2025-07-06T20:22:16.720Z [ERROR] ⚠️  High memory usage: 91.53%
2025-07-06T20:27:16.721Z [ERROR] ⚠️  High memory usage: 92.47%
2025-07-06T20:47:16.726Z [ERROR] ⚠️  High memory usage: 90.37%
2025-07-06T20:52:16.727Z [ERROR] ⚠️  High memory usage: 91.64%
2025-07-06T20:57:16.727Z [ERROR] ⚠️  High memory usage: 93.20%
2025-07-06T21:12:16.728Z [ERROR] ⚠️  High memory usage: 90.41%
2025-07-06T21:17:16.729Z [ERROR] ⚠️  High memory usage: 91.42%
2025-07-06T21:22:16.729Z [ERROR] ⚠️  High memory usage: 91.85%
2025-07-06T21:27:16.729Z [ERROR] ⚠️  High memory usage: 92.69%
2025-07-06T21:42:16.729Z [ERROR] ⚠️  High memory usage: 90.86%
2025-07-06T21:47:16.729Z [ERROR] ⚠️  High memory usage: 91.24%
2025-07-06T21:52:16.730Z [ERROR] ⚠️  High memory usage: 92.22%
2025-07-06T22:12:16.732Z [ERROR] ⚠️  High memory usage: 90.66%
2025-07-06T22:17:16.733Z [ERROR] ⚠️  High memory usage: 91.89%
2025-07-06T22:22:16.734Z [ERROR] ⚠️  High memory usage: 92.68%
2025-07-06T22:42:16.735Z [ERROR] ⚠️  High memory usage: 90.60%
2025-07-06T22:47:16.735Z [ERROR] ⚠️  High memory usage: 91.48%
2025-07-06T22:52:16.735Z [ERROR] ⚠️  High memory usage: 93.03%
2025-07-06T23:07:16.736Z [ERROR] ⚠️  High memory usage: 90.02%
2025-07-06T23:12:16.736Z [ERROR] ⚠️  High memory usage: 91.48%
2025-07-06T23:17:16.737Z [ERROR] ⚠️  High memory usage: 92.26%
2025-07-06T23:22:16.738Z [ERROR] ⚠️  High memory usage: 92.83%
2025-07-06T23:37:16.739Z [ERROR] ⚠️  High memory usage: 90.57%
2025-07-06T23:42:16.740Z [ERROR] ⚠️  High memory usage: 91.22%
2025-07-06T23:47:16.740Z [ERROR] ⚠️  High memory usage: 92.74%
2025-07-07T00:07:16.741Z [ERROR] ⚠️  High memory usage: 90.54%
2025-07-07T00:12:16.740Z [ERROR] ⚠️  High memory usage: 91.46%
2025-07-07T00:17:16.740Z [ERROR] ⚠️  High memory usage: 92.42%
2025-07-07T00:32:16.741Z [ERROR] ⚠️  High memory usage: 90.56%
2025-07-07T00:37:16.741Z [ERROR] ⚠️  High memory usage: 91.52%
2025-07-07T00:42:16.741Z [ERROR] ⚠️  High memory usage: 92.35%
2025-07-07T00:47:16.741Z [ERROR] ⚠️  High memory usage: 93.01%
2025-07-07T01:02:16.745Z [ERROR] ⚠️  High memory usage: 90.90%
2025-07-07T01:07:16.746Z [ERROR] ⚠️  High memory usage: 91.21%
2025-07-07T01:12:16.746Z [ERROR] ⚠️  High memory usage: 92.03%
2025-07-07T01:17:30.464Z [ERROR] Error fetching current UTC time: getaddrinfo EAI_AGAIN timeapi.io
2025-07-07T01:32:16.746Z [ERROR] ⚠️  High memory usage: 94.41%
2025-07-07T01:37:16.746Z [ERROR] ⚠️  High memory usage: 90.92%
2025-07-07T01:42:16.747Z [ERROR] ⚠️  High memory usage: 94.07%
2025-07-07T01:52:16.746Z [ERROR] ⚠️  High memory usage: 91.77%
2025-07-07T02:02:16.747Z [ERROR] ⚠️  High memory usage: 91.31%
2025-07-07T02:07:16.747Z [ERROR] ⚠️  High memory usage: 92.24%
2025-07-07T02:12:16.748Z [ERROR] ⚠️  High memory usage: 92.96%
2025-07-07T02:37:16.750Z [ERROR] ⚠️  High memory usage: 91.53%
2025-07-07T02:52:16.750Z [ERROR] ⚠️  High memory usage: 90.34%
2025-07-07T02:57:16.750Z [ERROR] ⚠️  High memory usage: 91.18%
2025-07-07T03:02:16.751Z [ERROR] ⚠️  High memory usage: 91.87%
2025-07-07T03:27:16.752Z [ERROR] ⚠️  High memory usage: 91.25%
2025-07-07T03:32:16.752Z [ERROR] ⚠️  High memory usage: 92.14%
2025-07-07T03:52:16.753Z [ERROR] ⚠️  High memory usage: 90.62%
2025-07-07T03:57:16.753Z [ERROR] ⚠️  High memory usage: 90.97%
2025-07-07T04:22:16.756Z [ERROR] ⚠️  High memory usage: 90.18%
2025-07-07T04:27:16.757Z [ERROR] ⚠️  High memory usage: 91.75%
2025-07-07T04:52:16.758Z [ERROR] ⚠️  High memory usage: 90.38%
2025-07-07T04:57:16.758Z [ERROR] ⚠️  High memory usage: 91.64%
2025-07-07T05:12:16.762Z [ERROR] ⚠️  High memory usage: 90.50%
2025-07-07T05:17:16.762Z [ERROR] ⚠️  High memory usage: 91.56%
2025-07-07T05:42:16.763Z [ERROR] ⚠️  High memory usage: 90.09%
2025-07-07T05:47:16.763Z [ERROR] ⚠️  High memory usage: 90.95%
2025-07-07T06:12:16.765Z [ERROR] ⚠️  High memory usage: 90.59%
2025-07-07T06:37:16.767Z [ERROR] ⚠️  High memory usage: 90.40%
2025-07-07T06:42:16.766Z [ERROR] ⚠️  High memory usage: 91.27%
2025-07-07T07:02:16.766Z [ERROR] ⚠️  High memory usage: 90.04%
2025-07-07T07:07:16.767Z [ERROR] ⚠️  High memory usage: 90.61%
2025-07-07T07:12:16.767Z [ERROR] ⚠️  High memory usage: 91.62%
2025-07-07T07:32:16.769Z [ERROR] ⚠️  High memory usage: 90.48%
2025-07-07T07:37:16.770Z [ERROR] ⚠️  High memory usage: 90.85%
2025-07-07T08:02:16.772Z [ERROR] ⚠️  High memory usage: 90.70%
2025-07-07T08:07:16.772Z [ERROR] ⚠️  High memory usage: 91.01%
2025-07-07T08:27:16.773Z [ERROR] ⚠️  High memory usage: 90.28%
2025-07-07T08:32:16.773Z [ERROR] ⚠️  High memory usage: 91.33%
2025-07-07T08:37:16.773Z [ERROR] ⚠️  High memory usage: 91.62%
2025-07-07T08:57:16.773Z [ERROR] ⚠️  High memory usage: 91.68%
2025-07-07T09:17:16.773Z [ERROR] ⚠️  High memory usage: 90.12%
2025-07-07T09:22:16.774Z [ERROR] ⚠️  High memory usage: 91.65%
2025-07-07T09:27:16.775Z [ERROR] ⚠️  High memory usage: 92.27%
2025-07-07T09:47:16.775Z [ERROR] ⚠️  High memory usage: 90.53%
2025-07-07T09:52:16.776Z [ERROR] ⚠️  High memory usage: 91.89%
2025-07-07T10:17:16.777Z [ERROR] ⚠️  High memory usage: 90.84%
2025-07-07T10:22:16.777Z [ERROR] ⚠️  High memory usage: 92.03%
2025-07-07T10:47:16.778Z [ERROR] ⚠️  High memory usage: 90.92%
2025-07-07T11:01:13.193Z [ERROR] [FFMPEG_STDERR] 4a56ecab-3911-4831-b91d-a4499f73e8a0: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/2072-2526-xv1q-k8cf-bzvm: Broken pipe
2025-07-07T11:01:13.939Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 4a56ecab-3911-4831-b91d-a4499f73e8a0
2025-07-07T11:07:16.781Z [ERROR] ⚠️  High memory usage: 90.00%
2025-07-07T11:12:16.782Z [ERROR] ⚠️  High memory usage: 90.46%
2025-07-07T11:17:16.783Z [ERROR] ⚠️  High memory usage: 91.69%
2025-07-07T11:37:16.783Z [ERROR] ⚠️  High memory usage: 90.12%
2025-07-07T11:42:16.784Z [ERROR] ⚠️  High memory usage: 91.31%
2025-07-07T11:47:16.784Z [ERROR] ⚠️  High memory usage: 91.66%
2025-07-07T12:07:16.787Z [ERROR] ⚠️  High memory usage: 90.82%
2025-07-07T12:12:16.787Z [ERROR] ⚠️  High memory usage: 91.82%
2025-07-07T12:32:16.790Z [ERROR] ⚠️  High memory usage: 90.05%
2025-07-07T12:37:16.789Z [ERROR] ⚠️  High memory usage: 90.16%
2025-07-07T12:42:16.790Z [ERROR] ⚠️  High memory usage: 91.07%
2025-07-07T13:02:16.791Z [ERROR] ⚠️  High memory usage: 90.16%
2025-07-07T13:07:16.791Z [ERROR] ⚠️  High memory usage: 91.20%
2025-07-07T13:32:16.793Z [ERROR] ⚠️  High memory usage: 90.87%
2025-07-07T13:37:16.793Z [ERROR] ⚠️  High memory usage: 91.43%
2025-07-07T14:02:16.797Z [ERROR] ⚠️  High memory usage: 90.91%
2025-07-07T14:07:16.797Z [ERROR] ⚠️  High memory usage: 91.99%
2025-07-07T14:32:16.800Z [ERROR] ⚠️  High memory usage: 91.07%
2025-07-07T14:57:16.801Z [ERROR] ⚠️  High memory usage: 90.95%
2025-07-07T15:02:16.802Z [ERROR] ⚠️  High memory usage: 91.87%
2025-07-07T15:22:16.803Z [ERROR] ⚠️  High memory usage: 90.01%
2025-07-07T15:27:16.803Z [ERROR] ⚠️  High memory usage: 90.83%
2025-07-07T15:32:16.803Z [ERROR] ⚠️  High memory usage: 91.66%
2025-07-07T15:52:16.803Z [ERROR] ⚠️  High memory usage: 90.53%
2025-07-07T15:57:16.803Z [ERROR] ⚠️  High memory usage: 91.50%
2025-07-07T16:22:16.810Z [ERROR] ⚠️  High memory usage: 90.96%
2025-07-07T16:27:16.810Z [ERROR] ⚠️  High memory usage: 91.80%
2025-07-07T16:52:16.812Z [ERROR] ⚠️  High memory usage: 90.74%
2025-07-07T16:57:16.812Z [ERROR] ⚠️  High memory usage: 91.84%
2025-07-07T17:22:16.816Z [ERROR] ⚠️  High memory usage: 91.58%
2025-07-07T17:47:16.817Z [ERROR] ⚠️  High memory usage: 90.43%
2025-07-07T17:52:16.818Z [ERROR] ⚠️  High memory usage: 91.46%
2025-07-07T18:17:16.819Z [ERROR] ⚠️  High memory usage: 90.72%
2025-07-07T18:47:16.822Z [ERROR] ⚠️  High memory usage: 90.72%
2025-07-07T19:07:16.822Z [ERROR] ⚠️  High memory usage: 90.86%
2025-07-07T19:12:16.823Z [ERROR] ⚠️  High memory usage: 91.73%
2025-07-07T19:37:16.825Z [ERROR] ⚠️  High memory usage: 90.15%
2025-07-07T19:42:16.825Z [ERROR] ⚠️  High memory usage: 91.55%
2025-07-07T20:07:16.828Z [ERROR] ⚠️  High memory usage: 90.18%
2025-07-07T20:27:16.828Z [ERROR] ⚠️  High memory usage: 90.50%
2025-07-07T20:32:16.827Z [ERROR] ⚠️  High memory usage: 91.57%
2025-07-07T20:57:16.830Z [ERROR] ⚠️  High memory usage: 90.47%
2025-07-07T21:02:16.830Z [ERROR] ⚠️  High memory usage: 91.83%
2025-07-07T21:17:16.833Z [ERROR] ⚠️  High memory usage: 90.03%
2025-07-07T21:22:16.833Z [ERROR] ⚠️  High memory usage: 90.56%
2025-07-07T21:27:16.834Z [ERROR] ⚠️  High memory usage: 91.95%
2025-07-07T21:52:16.837Z [ERROR] ⚠️  High memory usage: 90.61%
2025-07-07T22:17:16.840Z [ERROR] ⚠️  High memory usage: 90.57%
2025-07-07T22:22:16.841Z [ERROR] ⚠️  High memory usage: 91.86%
2025-07-07T22:47:16.841Z [ERROR] ⚠️  High memory usage: 90.65%
2025-07-07T22:52:16.842Z [ERROR] ⚠️  High memory usage: 91.34%
2025-07-07T23:17:16.845Z [ERROR] ⚠️  High memory usage: 90.48%
2025-07-07T23:22:16.845Z [ERROR] ⚠️  High memory usage: 91.53%
2025-07-07T23:42:16.845Z [ERROR] ⚠️  High memory usage: 90.17%
2025-07-07T23:47:16.846Z [ERROR] ⚠️  High memory usage: 90.78%
2025-07-08T00:17:16.848Z [ERROR] ⚠️  High memory usage: 90.98%
2025-07-08T00:42:16.850Z [ERROR] ⚠️  High memory usage: 90.28%
2025-07-08T00:47:16.850Z [ERROR] ⚠️  High memory usage: 91.35%
2025-07-08T01:12:16.853Z [ERROR] ⚠️  High memory usage: 90.45%
2025-07-08T01:17:16.853Z [ERROR] ⚠️  High memory usage: 91.69%
2025-07-08T01:42:16.855Z [ERROR] ⚠️  High memory usage: 93.55%
2025-07-08T01:47:16.855Z [ERROR] ⚠️  High memory usage: 91.13%
2025-07-08T01:57:16.855Z [ERROR] ⚠️  High memory usage: 91.37%
2025-07-08T02:02:16.857Z [ERROR] ⚠️  High memory usage: 92.26%
2025-07-08T02:32:16.858Z [ERROR] ⚠️  High memory usage: 91.32%
2025-07-08T02:57:16.860Z [ERROR] ⚠️  High memory usage: 90.77%
2025-07-08T03:27:16.863Z [ERROR] ⚠️  High memory usage: 90.87%
2025-07-08T03:52:16.868Z [ERROR] ⚠️  High memory usage: 90.38%
2025-07-08T03:57:16.869Z [ERROR] ⚠️  High memory usage: 91.06%
2025-07-08T05:07:16.874Z [ERROR] ⚠️  High memory usage: 90.57%
2025-07-08T05:12:16.874Z [ERROR] ⚠️  High memory usage: 91.55%
2025-07-08T05:37:16.875Z [ERROR] ⚠️  High memory usage: 90.79%
2025-07-08T05:42:16.875Z [ERROR] ⚠️  High memory usage: 91.60%
2025-07-08T06:02:16.878Z [ERROR] ⚠️  High memory usage: 90.37%
2025-07-08T06:07:16.878Z [ERROR] ⚠️  High memory usage: 91.24%
2025-07-08T06:32:16.879Z [ERROR] ⚠️  High memory usage: 90.62%
2025-07-08T06:37:16.879Z [ERROR] ⚠️  High memory usage: 91.48%
2025-07-08T06:47:16.880Z [ERROR] ⚠️  High memory usage: 91.15%
2025-07-08T06:52:16.879Z [ERROR] ⚠️  High memory usage: 91.84%
2025-07-08T07:17:16.881Z [ERROR] ⚠️  High memory usage: 90.16%
2025-07-08T07:47:16.887Z [ERROR] ⚠️  High memory usage: 90.76%
2025-07-08T08:42:16.893Z [ERROR] ⚠️  High memory usage: 90.22%
2025-07-08T09:12:16.897Z [ERROR] ⚠️  High memory usage: 90.11%
2025-07-08T09:37:16.899Z [ERROR] ⚠️  High memory usage: 90.09%
2025-07-08T10:07:16.903Z [ERROR] ⚠️  High memory usage: 90.59%
2025-07-08T10:17:25.205Z [ERROR] Error fetching current UTC time: getaddrinfo EAI_AGAIN timeapi.io
2025-07-08T10:17:35.234Z [ERROR] Error fetching current UTC time: getaddrinfo EAI_AGAIN timeapi.io
2025-07-08T11:02:16.908Z [ERROR] ⚠️  High memory usage: 90.16%
2025-07-08T12:22:16.916Z [ERROR] ⚠️  High memory usage: 90.50%
2025-07-08T13:42:16.920Z [ERROR] ⚠️  High memory usage: 90.16%
2025-07-08T13:52:16.921Z [ERROR] ⚠️  High memory usage: 91.07%
2025-07-08T13:57:16.922Z [ERROR] ⚠️  High memory usage: 91.35%
2025-07-08T14:52:16.928Z [ERROR] ⚠️  High memory usage: 90.38%
2025-07-08T15:17:16.931Z [ERROR] ⚠️  High memory usage: 90.68%
2025-07-08T16:12:16.934Z [ERROR] ⚠️  High memory usage: 90.20%
2025-07-08T16:17:16.934Z [ERROR] ⚠️  High memory usage: 90.84%
2025-07-08T16:29:38.384Z [ERROR] [FFMPEG_STDERR] 282fec21-3512-4f0e-a193-c7a0fb918dfe: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/fc4e-41fg-d62d-4u4x-62t8: Broken pipe
2025-07-08T16:29:39.200Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 282fec21-3512-4f0e-a193-c7a0fb918dfe
2025-07-08T16:47:16.937Z [ERROR] ⚠️  High memory usage: 91.35%
2025-07-08T17:17:16.941Z [ERROR] ⚠️  High memory usage: 91.03%
2025-07-08T17:42:16.945Z [ERROR] ⚠️  High memory usage: 90.89%
2025-07-08T18:07:16.945Z [ERROR] ⚠️  High memory usage: 90.07%
2025-07-08T18:08:46.213Z [ERROR] [FFMPEG_STDERR] 282fec21-3512-4f0e-a193-c7a0fb918dfe: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/fc4e-41fg-d62d-4u4x-62t8: Connection reset by peer
2025-07-08T18:08:47.022Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 282fec21-3512-4f0e-a193-c7a0fb918dfe
2025-07-08T18:12:16.946Z [ERROR] ⚠️  High memory usage: 91.39%
2025-07-08T18:32:16.949Z [ERROR] ⚠️  High memory usage: 90.42%
2025-07-08T18:37:16.949Z [ERROR] ⚠️  High memory usage: 91.55%
2025-07-08T18:57:16.952Z [ERROR] ⚠️  High memory usage: 90.02%
2025-07-08T19:02:16.952Z [ERROR] ⚠️  High memory usage: 90.40%
2025-07-08T19:27:16.951Z [ERROR] ⚠️  High memory usage: 90.38%
2025-07-08T19:32:16.951Z [ERROR] ⚠️  High memory usage: 91.44%
2025-07-08T19:57:16.952Z [ERROR] ⚠️  High memory usage: 90.30%
2025-07-08T20:27:16.952Z [ERROR] ⚠️  High memory usage: 91.02%
2025-07-08T20:52:16.954Z [ERROR] ⚠️  High memory usage: 90.38%
2025-07-08T20:57:16.954Z [ERROR] ⚠️  High memory usage: 91.46%
2025-07-08T21:22:16.956Z [ERROR] ⚠️  High memory usage: 91.17%
2025-07-08T21:47:16.958Z [ERROR] ⚠️  High memory usage: 90.08%
2025-07-08T21:52:16.959Z [ERROR] ⚠️  High memory usage: 90.90%
2025-07-08T22:01:16.205Z [ERROR] [FFMPEG_STDERR] 702ac857-edaf-4617-813d-d3ee67b777ab: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/j572-ps0b-sbkf-z0mk-1467: Broken pipe
2025-07-08T22:01:16.947Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 702ac857-edaf-4617-813d-d3ee67b777ab
2025-07-08T22:17:16.960Z [ERROR] ⚠️  High memory usage: 90.77%
2025-07-08T22:47:16.965Z [ERROR] ⚠️  High memory usage: 90.86%
2025-07-08T23:17:16.967Z [ERROR] ⚠️  High memory usage: 91.15%
2025-07-08T23:42:16.972Z [ERROR] ⚠️  High memory usage: 90.42%
2025-07-08T23:47:16.973Z [ERROR] ⚠️  High memory usage: 91.45%
2025-07-09T00:12:16.976Z [ERROR] ⚠️  High memory usage: 91.18%
2025-07-09T00:37:16.976Z [ERROR] ⚠️  High memory usage: 90.11%
2025-07-09T00:42:16.977Z [ERROR] ⚠️  High memory usage: 90.49%
2025-07-09T01:07:16.981Z [ERROR] ⚠️  High memory usage: 90.13%
2025-07-09T01:12:16.982Z [ERROR] ⚠️  High memory usage: 90.44%
2025-07-09T01:37:16.986Z [ERROR] ⚠️  High memory usage: 90.96%
2025-07-09T02:02:16.986Z [ERROR] ⚠️  High memory usage: 90.06%
2025-07-09T02:07:16.986Z [ERROR] ⚠️  High memory usage: 91.05%
2025-07-09T02:17:16.986Z [ERROR] ⚠️  High memory usage: 91.40%
2025-07-09T02:47:16.990Z [ERROR] ⚠️  High memory usage: 91.27%
2025-07-09T03:12:16.993Z [ERROR] ⚠️  High memory usage: 90.42%
2025-07-09T03:17:16.993Z [ERROR] ⚠️  High memory usage: 91.92%
2025-07-09T03:37:16.995Z [ERROR] ⚠️  High memory usage: 90.12%
2025-07-09T03:42:16.996Z [ERROR] ⚠️  High memory usage: 90.96%
2025-07-09T04:07:16.997Z [ERROR] ⚠️  High memory usage: 90.55%
2025-07-09T04:12:16.998Z [ERROR] ⚠️  High memory usage: 91.39%
2025-07-09T04:37:17.000Z [ERROR] ⚠️  High memory usage: 91.16%
2025-07-09T05:02:17.001Z [ERROR] ⚠️  High memory usage: 90.57%
2025-07-09T05:07:17.001Z [ERROR] ⚠️  High memory usage: 91.40%
2025-07-09T05:32:17.004Z [ERROR] ⚠️  High memory usage: 90.58%
2025-07-09T05:37:17.003Z [ERROR] ⚠️  High memory usage: 91.41%
2025-07-09T06:02:17.006Z [ERROR] ⚠️  High memory usage: 91.02%
2025-07-09T06:52:17.010Z [ERROR] ⚠️  High memory usage: 91.05%
2025-07-09T06:57:17.011Z [ERROR] ⚠️  High memory usage: 91.84%
2025-07-09T07:52:17.018Z [ERROR] ⚠️  High memory usage: 90.44%
2025-07-09T08:22:17.021Z [ERROR] ⚠️  High memory usage: 90.71%
2025-07-09T08:52:17.023Z [ERROR] ⚠️  High memory usage: 90.28%
2025-07-09T09:17:17.025Z [ERROR] ⚠️  High memory usage: 90.04%
2025-07-09T09:47:17.028Z [ERROR] ⚠️  High memory usage: 90.05%
2025-07-09T09:52:17.028Z [ERROR] ⚠️  High memory usage: 91.27%
2025-07-09T10:17:17.030Z [ERROR] ⚠️  High memory usage: 90.52%
2025-07-09T10:47:17.031Z [ERROR] ⚠️  High memory usage: 91.28%
2025-07-09T11:17:17.034Z [ERROR] ⚠️  High memory usage: 90.63%
2025-07-09T11:29:33.242Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-07-09T11:29:33.429Z [ERROR] [FFMPEG_STDERR] ba5857c7-1ea6-4464-b60a-440ba8b877fc: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/murk-epb3-1gjj-v5df-9h70: Broken pipe
2025-07-09T11:29:34.688Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-07-09T11:29:34.688Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream ba5857c7-1ea6-4464-b60a-440ba8b877fc
2025-07-09T11:47:17.036Z [ERROR] ⚠️  High memory usage: 91.19%
2025-07-09T12:12:17.038Z [ERROR] ⚠️  High memory usage: 90.45%
2025-07-09T12:17:17.038Z [ERROR] ⚠️  High memory usage: 91.60%
2025-07-09T12:42:17.041Z [ERROR] ⚠️  High memory usage: 90.73%
2025-07-09T12:47:17.042Z [ERROR] ⚠️  High memory usage: 92.22%
2025-07-09T13:02:17.042Z [ERROR] ⚠️  High memory usage: 90.66%
2025-07-09T13:07:17.043Z [ERROR] ⚠️  High memory usage: 91.46%
2025-07-09T13:27:17.047Z [ERROR] ⚠️  High memory usage: 90.82%
2025-07-09T13:32:17.048Z [ERROR] ⚠️  High memory usage: 91.19%
2025-07-09T13:41:43.581Z [ERROR] [FFMPEG_STDERR] 65dd1f3d-eb1a-495c-a804-b5471b53112d: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/7wgq-svse-mxj4-ttra-258p: Broken pipe
2025-07-09T13:41:44.327Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 65dd1f3d-eb1a-495c-a804-b5471b53112d
2025-07-09T13:41:44.791Z [ERROR] [FFMPEG_STDERR] f1d80bca-04a7-4fa6-a31d-d9f25b4d4b4b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/7qkm-e8h9-cd4x-sq11-65gs: Broken pipe
2025-07-09T13:41:44.812Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream f1d80bca-04a7-4fa6-a31d-d9f25b4d4b4b
2025-07-09T13:41:44.942Z [ERROR] [FFMPEG_STDERR] ae8f8bdd-f37f-4a62-b6eb-760571bc86a2: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/sqew-1dvz-e9gr-adg4-2sqm: Broken pipe
2025-07-09T13:41:44.964Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream ae8f8bdd-f37f-4a62-b6eb-760571bc86a2
2025-07-09T13:41:45.003Z [ERROR] [FFMPEG_STDERR] f435961a-69bb-4b81-848b-30197d11f752: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/tjbg-kyfa-7k5x-hqe8-9fqr: Broken pipe
2025-07-09T13:41:45.024Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream f435961a-69bb-4b81-848b-30197d11f752
2025-07-09T13:52:17.050Z [ERROR] ⚠️  High memory usage: 90.18%
2025-07-09T13:57:17.050Z [ERROR] ⚠️  High memory usage: 91.03%
2025-07-09T14:02:17.051Z [ERROR] ⚠️  High memory usage: 91.97%
2025-07-09T14:22:17.051Z [ERROR] ⚠️  High memory usage: 90.37%
2025-07-09T14:27:17.051Z [ERROR] ⚠️  High memory usage: 91.38%
2025-07-09T14:32:17.052Z [ERROR] ⚠️  High memory usage: 92.32%
2025-07-09T14:52:17.053Z [ERROR] ⚠️  High memory usage: 90.81%
2025-07-09T14:57:17.053Z [ERROR] ⚠️  High memory usage: 90.67%
2025-07-09T15:17:17.057Z [ERROR] ⚠️  High memory usage: 90.06%
2025-07-09T15:22:17.058Z [ERROR] ⚠️  High memory usage: 91.17%
2025-07-09T15:27:17.059Z [ERROR] ⚠️  High memory usage: 91.74%
2025-07-09T15:47:17.059Z [ERROR] ⚠️  High memory usage: 90.04%
2025-07-09T15:52:17.060Z [ERROR] ⚠️  High memory usage: 91.36%
2025-07-09T15:55:39.195Z [ERROR] [FFMPEG_STDERR] 3383a90f-6927-4692-b610-d1df58f43f09: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/g7x5-6epf-ug1h-ybq7-d2ct: Broken pipe
2025-07-09T15:55:39.991Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 3383a90f-6927-4692-b610-d1df58f43f09
2025-07-09T15:57:17.060Z [ERROR] ⚠️  High memory usage: 91.60%
2025-07-09T16:17:17.061Z [ERROR] ⚠️  High memory usage: 90.30%
2025-07-09T16:22:17.061Z [ERROR] ⚠️  High memory usage: 90.88%
2025-07-09T16:47:17.063Z [ERROR] ⚠️  High memory usage: 90.22%
2025-07-09T16:52:17.063Z [ERROR] ⚠️  High memory usage: 91.49%
2025-07-09T17:07:17.065Z [ERROR] ⚠️  High memory usage: 90.62%
2025-07-09T17:12:17.066Z [ERROR] ⚠️  High memory usage: 91.44%
2025-07-09T17:17:17.066Z [ERROR] ⚠️  High memory usage: 92.35%
2025-07-09T17:37:17.066Z [ERROR] ⚠️  High memory usage: 90.19%
2025-07-09T17:42:17.066Z [ERROR] ⚠️  High memory usage: 91.41%
2025-07-09T18:05:19.339Z [ERROR] [FFMPEG_STDERR] ae8f8bdd-f37f-4a62-b6eb-760571bc86a2: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/sqew-1dvz-e9gr-adg4-2sqm: Broken pipe
2025-07-09T18:05:20.104Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream ae8f8bdd-f37f-4a62-b6eb-760571bc86a2
2025-07-09T18:05:21.751Z [ERROR] [FFMPEG_STDERR] f1d80bca-04a7-4fa6-a31d-d9f25b4d4b4b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/7qkm-e8h9-cd4x-sq11-65gs: Broken pipe
2025-07-09T18:05:21.772Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream f1d80bca-04a7-4fa6-a31d-d9f25b4d4b4b
2025-07-09T18:05:21.840Z [ERROR] [FFMPEG_STDERR] 4a56ecab-3911-4831-b91d-a4499f73e8a0: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/2072-2526-xv1q-k8cf-bzvm: Broken pipe
2025-07-09T18:05:21.856Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 4a56ecab-3911-4831-b91d-a4499f73e8a0
2025-07-09T18:05:21.894Z [ERROR] [FFMPEG_STDERR] 282fec21-3512-4f0e-a193-c7a0fb918dfe: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/fc4e-41fg-d62d-4u4x-62t8: Broken pipe
2025-07-09T18:05:21.910Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 282fec21-3512-4f0e-a193-c7a0fb918dfe
2025-07-09T18:05:22.947Z [ERROR] [FFMPEG_STDERR] f435961a-69bb-4b81-848b-30197d11f752: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/tjbg-kyfa-7k5x-hqe8-9fqr: Broken pipe
2025-07-09T18:05:22.966Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream f435961a-69bb-4b81-848b-30197d11f752
2025-07-09T18:05:23.559Z [ERROR] [FFMPEG_STDERR] 702ac857-edaf-4617-813d-d3ee67b777ab: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/j572-ps0b-sbkf-z0mk-1467: Broken pipe
2025-07-09T18:05:23.583Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 702ac857-edaf-4617-813d-d3ee67b777ab
2025-07-09T18:05:24.321Z [ERROR] [FFMPEG_STDERR] ae8f8bdd-f37f-4a62-b6eb-760571bc86a2: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/sqew-1dvz-e9gr-adg4-2sqm: Broken pipe
2025-07-09T18:05:24.369Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream ae8f8bdd-f37f-4a62-b6eb-760571bc86a2
2025-07-09T18:07:17.066Z [ERROR] ⚠️  High memory usage: 91.19%
2025-07-09T18:27:17.066Z [ERROR] ⚠️  High memory usage: 90.10%
2025-07-09T18:32:17.067Z [ERROR] ⚠️  High memory usage: 91.33%
2025-07-09T18:37:17.068Z [ERROR] ⚠️  High memory usage: 92.34%
2025-07-09T18:57:17.069Z [ERROR] ⚠️  High memory usage: 90.33%
2025-07-09T19:02:17.071Z [ERROR] ⚠️  High memory usage: 91.02%
2025-07-09T19:27:17.073Z [ERROR] ⚠️  High memory usage: 90.44%
2025-07-09T19:32:17.074Z [ERROR] ⚠️  High memory usage: 91.21%
2025-07-09T19:47:17.076Z [ERROR] ⚠️  High memory usage: 90.13%
2025-07-09T19:52:17.077Z [ERROR] ⚠️  High memory usage: 91.13%
2025-07-09T19:57:17.078Z [ERROR] ⚠️  High memory usage: 92.12%
2025-07-09T20:17:17.079Z [ERROR] ⚠️  High memory usage: 90.90%
2025-07-09T20:22:17.080Z [ERROR] ⚠️  High memory usage: 91.46%
2025-07-09T20:47:17.086Z [ERROR] ⚠️  High memory usage: 90.99%
2025-07-09T21:12:17.088Z [ERROR] ⚠️  High memory usage: 90.05%
2025-07-09T21:17:17.089Z [ERROR] ⚠️  High memory usage: 91.00%
2025-07-09T21:42:17.092Z [ERROR] ⚠️  High memory usage: 90.17%
2025-07-09T21:47:17.093Z [ERROR] ⚠️  High memory usage: 91.66%
2025-07-09T22:12:17.095Z [ERROR] ⚠️  High memory usage: 90.56%
2025-07-09T22:17:17.096Z [ERROR] ⚠️  High memory usage: 91.58%
2025-07-09T22:37:17.098Z [ERROR] ⚠️  High memory usage: 90.21%
2025-07-09T22:42:17.098Z [ERROR] ⚠️  High memory usage: 91.15%
2025-07-09T22:47:17.098Z [ERROR] ⚠️  High memory usage: 91.66%
2025-07-09T23:07:17.100Z [ERROR] ⚠️  High memory usage: 90.39%
2025-07-09T23:12:17.100Z [ERROR] ⚠️  High memory usage: 91.07%
2025-07-09T23:17:17.100Z [ERROR] ⚠️  High memory usage: 92.22%
2025-07-09T23:37:17.101Z [ERROR] ⚠️  High memory usage: 90.15%
2025-07-09T23:42:17.101Z [ERROR] ⚠️  High memory usage: 91.03%
2025-07-09T23:47:17.101Z [ERROR] ⚠️  High memory usage: 91.65%
2025-07-10T00:12:17.103Z [ERROR] ⚠️  High memory usage: 91.12%
2025-07-10T00:37:17.107Z [ERROR] ⚠️  High memory usage: 90.40%
2025-07-10T00:42:17.107Z [ERROR] ⚠️  High memory usage: 91.21%
2025-07-10T00:59:33.373Z [ERROR] [FFMPEG_STDERR] 282fec21-3512-4f0e-a193-c7a0fb918dfe: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/fc4e-41fg-d62d-4u4x-62t8: Connection reset by peer
2025-07-10T00:59:34.879Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 282fec21-3512-4f0e-a193-c7a0fb918dfe
2025-07-10T01:02:17.109Z [ERROR] ⚠️  High memory usage: 91.32%
2025-07-10T01:07:17.109Z [ERROR] ⚠️  High memory usage: 92.16%
2025-07-10T01:12:17.110Z [ERROR] ⚠️  High memory usage: 93.06%
2025-07-10T01:22:17.109Z [ERROR] ⚠️  High memory usage: 90.25%
2025-07-10T01:27:17.110Z [ERROR] ⚠️  High memory usage: 91.05%
2025-07-10T01:32:17.111Z [ERROR] ⚠️  High memory usage: 91.99%
2025-07-10T01:47:17.113Z [ERROR] ⚠️  High memory usage: 90.49%
2025-07-10T02:02:17.113Z [ERROR] ⚠️  High memory usage: 91.23%
2025-07-10T02:12:17.114Z [ERROR] ⚠️  High memory usage: 90.23%
2025-07-10T02:22:17.114Z [ERROR] ⚠️  High memory usage: 91.31%
2025-07-10T02:27:17.115Z [ERROR] ⚠️  High memory usage: 92.72%
2025-07-10T02:29:58.510Z [ERROR] Error processing Google Drive import: GaxiosError: Google Drive API has not been used in project 925009734189 before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/drive.googleapis.com/overview?project=925009734189 then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.
    at Gaxios._request (/home/<USER>/streamonpod/node_modules/gaxios/build/src/gaxios.js:142:23)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async processGoogleDriveImport (/home/<USER>/streamonpod/app.js:3068:26) {
  config: {
    url: 'https://www.googleapis.com/drive/v3/files/14t1inaMPzVed0IbvJt4c0n0D2u7itC24?fields=name%2CmimeType%2Csize&key=AIzaSyDphvGqDhPwnG21Lvaxu_hSy_yUKebJpl4',
    method: 'GET',
    apiVersion: '',
    userAgentDirectives: [ [Object] ],
    paramsSerializer: [Function (anonymous)],
    headers: {
      'x-goog-api-client': 'gdcl/7.2.0 gl-node/20.11.0',
      'Accept-Encoding': 'gzip',
      'User-Agent': 'google-api-nodejs-client/7.2.0 (gzip)'
    },
    params: {
      fields: 'name,mimeType,size',
      key: 'AIzaSyDphvGqDhPwnG21Lvaxu_hSy_yUKebJpl4'
    },
    validateStatus: [Function (anonymous)],
    retry: true,
    responseType: 'unknown',
    errorRedactor: [Function: defaultErrorRedactor],
    retryConfig: {
      currentRetryAttempt: 0,
      retry: 3,
      httpMethodsToRetry: [Array],
      noResponseRetries: 2,
      retryDelayMultiplier: 2,
      timeOfFirstRequest: 1752114598508,
      totalTimeout: 9007199254740991,
      maxRetryDelay: 9007199254740991,
      statusCodesToRetry: [Array]
    }
  },
  response: {
    config: {
      url: 'https://www.googleapis.com/drive/v3/files/14t1inaMPzVed0IbvJt4c0n0D2u7itC24?fields=name%2CmimeType%2Csize&key=AIzaSyDphvGqDhPwnG21Lvaxu_hSy_yUKebJpl4',
      method: 'GET',
      apiVersion: '',
      userAgentDirectives: [Array],
      paramsSerializer: [Function (anonymous)],
      headers: [Object],
      params: [Object],
      validateStatus: [Function (anonymous)],
      retry: true,
      responseType: 'unknown',
      errorRedactor: [Function: defaultErrorRedactor]
    },
    data: { error: [Object] },
    headers: {
      'alt-svc': 'h3=":443"; ma=2592000,h3-29=":443"; ma=2592000',
      'content-encoding': 'gzip',
      'content-type': 'application/json; charset=UTF-8',
      date: 'Thu, 10 Jul 2025 02:29:58 GMT',
      server: 'ESF',
      'transfer-encoding': 'chunked',
      'x-content-type-options': 'nosniff',
      'x-frame-options': 'SAMEORIGIN',
      'x-xss-protection': '0'
    },
    status: 403,
    statusText: 'Forbidden',
    request: {
      responseURL: 'https://www.googleapis.com/drive/v3/files/14t1inaMPzVed0IbvJt4c0n0D2u7itC24?fields=name%2CmimeType%2Csize&key=AIzaSyDphvGqDhPwnG21Lvaxu_hSy_yUKebJpl4'
    }
  },
  error: undefined,
  status: 403,
  code: 403,
  errors: [
    {
      message: 'Google Drive API has not been used in project 925009734189 before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/drive.googleapis.com/overview?project=925009734189 then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.',
      domain: 'usageLimits',
      reason: 'accessNotConfigured',
      extendedHelp: 'https://console.developers.google.com'
    }
  ],
  [Symbol(gaxios-gaxios-error)]: '6.7.1'
}
2025-07-10T02:32:17.115Z [ERROR] ⚠️  High memory usage: 91.56%
2025-07-10T02:42:17.115Z [ERROR] ⚠️  High memory usage: 92.59%
2025-07-10T03:02:17.116Z [ERROR] ⚠️  High memory usage: 92.03%
2025-07-10T03:07:17.116Z [ERROR] ⚠️  High memory usage: 90.88%
2025-07-10T03:12:17.117Z [ERROR] ⚠️  High memory usage: 92.54%
2025-07-10T03:17:17.117Z [ERROR] ⚠️  High memory usage: 93.22%
2025-07-10T03:42:17.119Z [ERROR] ⚠️  High memory usage: 91.73%
2025-07-10T03:47:17.119Z [ERROR] ⚠️  High memory usage: 91.90%
2025-07-10T03:52:17.119Z [ERROR] ⚠️  High memory usage: 90.17%
2025-07-10T03:57:17.119Z [ERROR] ⚠️  High memory usage: 91.01%
2025-07-10T04:02:17.119Z [ERROR] ⚠️  High memory usage: 92.65%
2025-07-10T04:07:17.120Z [ERROR] ⚠️  High memory usage: 92.96%
2025-07-10T04:12:17.121Z [ERROR] ⚠️  High memory usage: 92.94%
2025-07-10T04:22:17.121Z [ERROR] ⚠️  High memory usage: 93.35%
2025-07-10T04:27:17.122Z [ERROR] ⚠️  High memory usage: 92.48%
2025-07-10T04:37:17.123Z [ERROR] ⚠️  High memory usage: 90.94%
2025-07-10T04:42:17.123Z [ERROR] ⚠️  High memory usage: 91.32%
2025-07-10T04:52:17.123Z [ERROR] ⚠️  High memory usage: 93.58%
2025-07-10T04:57:17.123Z [ERROR] ⚠️  High memory usage: 93.55%
2025-07-10T05:02:17.123Z [ERROR] ⚠️  High memory usage: 93.78%
2025-07-10T05:07:17.123Z [ERROR] ⚠️  High memory usage: 91.99%
2025-07-10T05:12:17.123Z [ERROR] ⚠️  High memory usage: 93.34%
2025-07-10T05:12:30.169Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-07-10T05:12:30.218Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-07-10T05:27:17.126Z [ERROR] ⚠️  High memory usage: 91.81%
2025-07-10T05:32:17.128Z [ERROR] ⚠️  High memory usage: 94.04%
2025-07-10T05:42:17.127Z [ERROR] ⚠️  High memory usage: 92.22%
2025-07-10T05:47:17.127Z [ERROR] ⚠️  High memory usage: 93.72%
2025-07-10T05:52:17.128Z [ERROR] ⚠️  High memory usage: 90.17%
2025-07-10T05:57:17.129Z [ERROR] ⚠️  High memory usage: 93.07%
2025-07-10T06:17:17.131Z [ERROR] ⚠️  High memory usage: 90.09%
2025-07-10T06:22:17.131Z [ERROR] ⚠️  High memory usage: 91.01%
2025-07-10T06:42:17.133Z [ERROR] ⚠️  High memory usage: 91.04%
2025-07-10T06:47:17.133Z [ERROR] ⚠️  High memory usage: 91.34%
2025-07-10T06:52:17.134Z [ERROR] ⚠️  High memory usage: 92.72%
2025-07-10T07:07:17.134Z [ERROR] ⚠️  High memory usage: 90.58%
2025-07-10T07:27:17.135Z [ERROR] ⚠️  High memory usage: 90.87%
2025-07-10T07:32:17.136Z [ERROR] ⚠️  High memory usage: 90.98%
2025-07-10T07:37:17.136Z [ERROR] ⚠️  High memory usage: 91.92%
2025-07-10T07:57:17.140Z [ERROR] ⚠️  High memory usage: 91.18%
2025-07-10T08:02:17.140Z [ERROR] ⚠️  High memory usage: 91.98%
2025-07-10T08:07:17.140Z [ERROR] ⚠️  High memory usage: 92.83%
2025-07-10T08:22:17.141Z [ERROR] ⚠️  High memory usage: 90.64%
2025-07-10T08:27:17.142Z [ERROR] ⚠️  High memory usage: 91.53%
2025-07-10T08:32:17.143Z [ERROR] ⚠️  High memory usage: 91.76%
2025-07-10T08:52:17.144Z [ERROR] ⚠️  High memory usage: 90.33%
2025-07-10T08:57:17.144Z [ERROR] ⚠️  High memory usage: 91.95%
2025-07-10T09:02:17.145Z [ERROR] ⚠️  High memory usage: 91.98%
2025-07-10T09:17:17.145Z [ERROR] ⚠️  High memory usage: 90.11%
2025-07-10T09:27:17.147Z [ERROR] ⚠️  High memory usage: 91.41%
2025-07-10T09:32:17.147Z [ERROR] ⚠️  High memory usage: 92.99%
2025-07-10T09:52:17.148Z [ERROR] ⚠️  High memory usage: 91.49%
2025-07-10T09:57:17.148Z [ERROR] ⚠️  High memory usage: 91.91%
2025-07-10T10:22:17.148Z [ERROR] ⚠️  High memory usage: 91.83%
2025-07-10T10:27:17.149Z [ERROR] ⚠️  High memory usage: 92.06%
2025-07-10T10:47:17.152Z [ERROR] ⚠️  High memory usage: 90.76%
2025-07-10T10:52:17.152Z [ERROR] ⚠️  High memory usage: 91.38%
2025-07-10T10:57:17.153Z [ERROR] ⚠️  High memory usage: 92.82%
2025-07-10T11:12:17.156Z [ERROR] ⚠️  High memory usage: 90.05%
2025-07-10T11:17:17.156Z [ERROR] ⚠️  High memory usage: 91.18%
2025-07-10T11:22:17.157Z [ERROR] ⚠️  High memory usage: 92.12%
2025-07-10T11:42:17.160Z [ERROR] ⚠️  High memory usage: 90.41%
2025-07-10T11:47:17.159Z [ERROR] ⚠️  High memory usage: 90.80%
2025-07-10T11:52:17.160Z [ERROR] ⚠️  High memory usage: 92.73%
2025-07-10T12:12:17.162Z [ERROR] ⚠️  High memory usage: 90.02%
2025-07-10T12:17:17.161Z [ERROR] ⚠️  High memory usage: 91.42%
2025-07-10T12:22:17.161Z [ERROR] ⚠️  High memory usage: 93.04%
2025-07-10T12:37:17.162Z [ERROR] ⚠️  High memory usage: 90.40%
2025-07-10T12:42:17.162Z [ERROR] ⚠️  High memory usage: 91.16%
2025-07-10T12:47:17.162Z [ERROR] ⚠️  High memory usage: 91.64%
2025-07-10T13:07:17.165Z [ERROR] ⚠️  High memory usage: 90.57%
2025-07-10T13:12:17.165Z [ERROR] ⚠️  High memory usage: 91.43%
2025-07-10T13:13:17.636Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Connection reset by peer
2025-07-10T13:13:17.658Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-07-10T13:17:17.166Z [ERROR] ⚠️  High memory usage: 92.01%
2025-07-10T13:37:17.168Z [ERROR] ⚠️  High memory usage: 90.55%
2025-07-10T13:42:17.169Z [ERROR] ⚠️  High memory usage: 91.63%
2025-07-10T13:46:14.351Z [ERROR] [FFMPEG_STDERR] ae8f8bdd-f37f-4a62-b6eb-760571bc86a2: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/sqew-1dvz-e9gr-adg4-2sqm: Broken pipe
2025-07-10T13:46:14.375Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream ae8f8bdd-f37f-4a62-b6eb-760571bc86a2
2025-07-10T13:47:17.169Z [ERROR] ⚠️  High memory usage: 92.42%
2025-07-10T14:07:17.169Z [ERROR] ⚠️  High memory usage: 91.37%
2025-07-10T14:22:17.170Z [ERROR] ⚠️  High memory usage: 90.49%
2025-07-10T14:27:17.171Z [ERROR] ⚠️  High memory usage: 90.84%
2025-07-10T14:32:17.172Z [ERROR] ⚠️  High memory usage: 92.19%
2025-07-10T14:37:17.172Z [ERROR] ⚠️  High memory usage: 93.23%
2025-07-10T14:57:17.174Z [ERROR] ⚠️  High memory usage: 91.16%
2025-07-10T15:02:17.174Z [ERROR] ⚠️  High memory usage: 92.12%
2025-07-10T15:07:17.175Z [ERROR] ⚠️  High memory usage: 92.92%
2025-07-10T15:27:17.177Z [ERROR] ⚠️  High memory usage: 90.86%
2025-07-10T15:32:17.177Z [ERROR] ⚠️  High memory usage: 92.04%
2025-07-10T15:47:17.180Z [ERROR] ⚠️  High memory usage: 90.12%
2025-07-10T15:52:17.180Z [ERROR] ⚠️  High memory usage: 91.34%
2025-07-10T15:57:17.180Z [ERROR] ⚠️  High memory usage: 92.02%
2025-07-10T16:07:17.181Z [ERROR] ⚠️  High memory usage: 90.23%
2025-07-10T16:12:17.181Z [ERROR] ⚠️  High memory usage: 90.53%
2025-07-10T16:17:17.181Z [ERROR] ⚠️  High memory usage: 91.44%
2025-07-10T16:22:17.181Z [ERROR] ⚠️  High memory usage: 92.95%
2025-07-10T16:37:17.184Z [ERROR] ⚠️  High memory usage: 90.19%
2025-07-10T16:42:17.184Z [ERROR] ⚠️  High memory usage: 91.02%
2025-07-10T16:47:17.185Z [ERROR] ⚠️  High memory usage: 91.21%
2025-07-10T16:52:17.186Z [ERROR] ⚠️  High memory usage: 93.10%
2025-07-10T16:58:15.756Z [ERROR] [FFMPEG_STDERR] 282fec21-3512-4f0e-a193-c7a0fb918dfe: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/fc4e-41fg-d62d-4u4x-62t8: Broken pipe
2025-07-10T16:58:15.781Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 282fec21-3512-4f0e-a193-c7a0fb918dfe
2025-07-10T17:07:08.018Z [ERROR] [FFMPEG_STDERR] 2ada3496-6fa8-4ef5-8a8e-52d07c25e68c: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/t0b4-3w3s-8cp7-mr15-6xak: Broken pipe
2025-07-10T17:07:08.040Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 2ada3496-6fa8-4ef5-8a8e-52d07c25e68c
2025-07-10T17:07:17.187Z [ERROR] ⚠️  High memory usage: 90.16%
2025-07-10T17:12:17.188Z [ERROR] ⚠️  High memory usage: 91.58%
2025-07-10T17:17:17.188Z [ERROR] ⚠️  High memory usage: 91.79%
2025-07-10T17:24:06.956Z [ERROR] [FFMPEG_STDERR] ae8f8bdd-f37f-4a62-b6eb-760571bc86a2: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/sqew-1dvz-e9gr-adg4-2sqm: Broken pipe
2025-07-10T17:24:06.980Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream ae8f8bdd-f37f-4a62-b6eb-760571bc86a2
2025-07-10T17:37:17.191Z [ERROR] ⚠️  High memory usage: 90.05%
2025-07-10T17:40:38.598Z [ERROR] [FFMPEG_STDERR] c42d8cd2-92a1-4556-9774-45b0a234483a: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/4r1g-fegy-fbpm-w9mt-ck2t: Connection reset by peer
2025-07-10T17:40:38.621Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream c42d8cd2-92a1-4556-9774-45b0a234483a
2025-07-10T17:42:17.191Z [ERROR] ⚠️  High memory usage: 91.06%
2025-07-10T17:47:17.191Z [ERROR] ⚠️  High memory usage: 92.10%
2025-07-10T17:55:21.551Z [ERROR] [FFMPEG_STDERR] 3383a90f-6927-4692-b610-d1df58f43f09: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/g7x5-6epf-ug1h-ybq7-d2ct: Broken pipe
2025-07-10T17:55:21.572Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 3383a90f-6927-4692-b610-d1df58f43f09
2025-07-10T17:58:48.228Z [ERROR] [FFMPEG_STDERR] 3383a90f-6927-4692-b610-d1df58f43f09: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/g7x5-6epf-ug1h-ybq7-d2ct: Connection reset by peer
2025-07-10T17:58:48.239Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 3383a90f-6927-4692-b610-d1df58f43f09
2025-07-10T18:01:27.272Z [ERROR] [FFMPEG_STDERR] 3383a90f-6927-4692-b610-d1df58f43f09: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/g7x5-6epf-ug1h-ybq7-d2ct: Broken pipe
2025-07-10T18:01:27.284Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 3383a90f-6927-4692-b610-d1df58f43f09
2025-07-10T18:02:17.191Z [ERROR] ⚠️  High memory usage: 90.30%
2025-07-10T18:03:46.129Z [ERROR] [FFMPEG_STDERR] 8a410b2a-fbcb-46a6-b710-9bdd0036be5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/u9d6-fqj6-qv5a-amfw-400z: Broken pipe
2025-07-10T18:03:46.145Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 8a410b2a-fbcb-46a6-b710-9bdd0036be5b
2025-07-10T18:07:17.192Z [ERROR] ⚠️  High memory usage: 91.64%
2025-07-10T18:10:09.437Z [ERROR] [FFMPEG_STDERR] ae8f8bdd-f37f-4a62-b6eb-760571bc86a2: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/sqew-1dvz-e9gr-adg4-2sqm: Broken pipe
2025-07-10T18:10:09.505Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream ae8f8bdd-f37f-4a62-b6eb-760571bc86a2
2025-07-10T18:12:17.193Z [ERROR] ⚠️  High memory usage: 92.08%
2025-07-10T18:27:17.194Z [ERROR] ⚠️  High memory usage: 90.21%
2025-07-10T18:28:25.914Z [ERROR] [FFMPEG_STDERR] be34f6aa-bdee-464d-b2c6-490151e0562c: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/zqyr-011w-r0x6-07y4-1u8v: Broken pipe
2025-07-10T18:28:25.935Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream be34f6aa-bdee-464d-b2c6-490151e0562c
2025-07-10T18:32:17.195Z [ERROR] ⚠️  High memory usage: 91.23%
2025-07-10T18:37:17.196Z [ERROR] ⚠️  High memory usage: 92.13%
2025-07-10T18:38:31.251Z [ERROR] [FFMPEG_STDERR] be652210-3216-4893-96cc-d87dc163237d: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/52wf-rete-q303-98eg-8312: Connection timed out
2025-07-10T18:38:31.278Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream be652210-3216-4893-96cc-d87dc163237d
2025-07-10T18:42:07.710Z [ERROR] [FFMPEG_STDERR] 702ac857-edaf-4617-813d-d3ee67b777ab: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/j572-ps0b-sbkf-z0mk-1467: Broken pipe
2025-07-10T18:42:07.732Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 702ac857-edaf-4617-813d-d3ee67b777ab
2025-07-10T18:44:26.437Z [ERROR] [FFMPEG_STDERR] 3383a90f-6927-4692-b610-d1df58f43f09: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/g7x5-6epf-ug1h-ybq7-d2ct: Connection reset by peer
2025-07-10T18:44:26.453Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 3383a90f-6927-4692-b610-d1df58f43f09
2025-07-10T18:46:45.128Z [ERROR] [FFMPEG_STDERR] 2ada3496-6fa8-4ef5-8a8e-52d07c25e68c: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/t0b4-3w3s-8cp7-mr15-6xak: Broken pipe
2025-07-10T18:46:45.145Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 2ada3496-6fa8-4ef5-8a8e-52d07c25e68c
2025-07-10T18:57:17.197Z [ERROR] ⚠️  High memory usage: 91.19%
2025-07-10T19:02:17.198Z [ERROR] ⚠️  High memory usage: 91.21%
2025-07-10T19:07:17.199Z [ERROR] ⚠️  High memory usage: 92.17%
2025-07-10T19:08:38.299Z [ERROR] [FFMPEG_STDERR] d1daeb9c-56d4-4fa0-ad17-e9b2855a3d5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/syc8-cwc2-drah-rpmd-279y: Connection reset by peer
2025-07-10T19:08:38.322Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream d1daeb9c-56d4-4fa0-ad17-e9b2855a3d5b
2025-07-10T19:09:18.496Z [ERROR] [FFMPEG_STDERR] 282fec21-3512-4f0e-a193-c7a0fb918dfe: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/fc4e-41fg-d62d-4u4x-62t8: Broken pipe
2025-07-10T19:09:18.522Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 282fec21-3512-4f0e-a193-c7a0fb918dfe
2025-07-10T19:12:33.150Z [ERROR] [FFMPEG_STDERR] 65dd1f3d-eb1a-495c-a804-b5471b53112d: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/7wgq-svse-mxj4-ttra-258p: Broken pipe
2025-07-10T19:12:33.171Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 65dd1f3d-eb1a-495c-a804-b5471b53112d
2025-07-10T19:22:17.199Z [ERROR] ⚠️  High memory usage: 90.03%
2025-07-10T19:22:57.745Z [ERROR] [FFMPEG_STDERR] ae8f8bdd-f37f-4a62-b6eb-760571bc86a2: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/sqew-1dvz-e9gr-adg4-2sqm: Broken pipe
2025-07-10T19:22:57.764Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream ae8f8bdd-f37f-4a62-b6eb-760571bc86a2
2025-07-10T19:27:17.200Z [ERROR] ⚠️  High memory usage: 91.54%
2025-07-10T19:32:17.201Z [ERROR] ⚠️  High memory usage: 92.56%
2025-07-10T19:52:17.201Z [ERROR] ⚠️  High memory usage: 91.09%
2025-07-10T19:57:17.202Z [ERROR] ⚠️  High memory usage: 92.12%
2025-07-10T20:17:17.203Z [ERROR] ⚠️  High memory usage: 90.28%
2025-07-10T20:22:17.204Z [ERROR] ⚠️  High memory usage: 91.27%
2025-07-10T20:27:17.204Z [ERROR] ⚠️  High memory usage: 92.29%
2025-07-10T20:42:17.204Z [ERROR] ⚠️  High memory usage: 92.74%
2025-07-10T21:02:17.205Z [ERROR] ⚠️  High memory usage: 90.65%
2025-07-10T21:07:17.205Z [ERROR] ⚠️  High memory usage: 90.98%
2025-07-10T21:12:17.206Z [ERROR] ⚠️  High memory usage: 91.19%
2025-07-10T21:14:04.108Z [ERROR] [FFMPEG_STDERR] ae8f8bdd-f37f-4a62-b6eb-760571bc86a2: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/sqew-1dvz-e9gr-adg4-2sqm: Broken pipe
2025-07-10T21:14:04.144Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream ae8f8bdd-f37f-4a62-b6eb-760571bc86a2
2025-07-10T21:32:17.208Z [ERROR] ⚠️  High memory usage: 91.18%
2025-07-10T21:37:17.208Z [ERROR] ⚠️  High memory usage: 92.17%
2025-07-10T21:57:17.208Z [ERROR] ⚠️  High memory usage: 90.56%
2025-07-10T22:02:17.210Z [ERROR] ⚠️  High memory usage: 91.83%
2025-07-10T22:22:17.212Z [ERROR] ⚠️  High memory usage: 90.53%
2025-07-10T22:27:17.213Z [ERROR] ⚠️  High memory usage: 90.85%
2025-07-10T22:32:17.213Z [ERROR] ⚠️  High memory usage: 92.44%
2025-07-10T22:52:17.216Z [ERROR] ⚠️  High memory usage: 91.34%
2025-07-10T22:57:17.216Z [ERROR] ⚠️  High memory usage: 91.83%
2025-07-10T23:02:17.217Z [ERROR] ⚠️  High memory usage: 93.98%
2025-07-10T23:04:33.720Z [ERROR] [FFMPEG_STDERR] 65dd1f3d-eb1a-495c-a804-b5471b53112d: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/7wgq-svse-mxj4-ttra-258p: End of file
2025-07-10T23:04:33.737Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 65dd1f3d-eb1a-495c-a804-b5471b53112d
2025-07-10T23:07:17.218Z [ERROR] ⚠️  High memory usage: 94.21%
2025-07-10T23:12:17.218Z [ERROR] ⚠️  High memory usage: 94.01%
2025-07-10T23:17:17.218Z [ERROR] ⚠️  High memory usage: 94.24%
2025-07-10T23:27:17.219Z [ERROR] ⚠️  High memory usage: 90.74%
2025-07-10T23:32:17.220Z [ERROR] ⚠️  High memory usage: 91.91%
2025-07-10T23:37:17.221Z [ERROR] ⚠️  High memory usage: 92.96%
2025-07-10T23:42:17.220Z [ERROR] ⚠️  High memory usage: 93.56%
2025-07-11T00:02:17.221Z [ERROR] ⚠️  High memory usage: 91.77%
2025-07-11T00:07:17.221Z [ERROR] ⚠️  High memory usage: 91.93%
2025-07-11T00:12:17.221Z [ERROR] ⚠️  High memory usage: 93.54%
2025-07-11T00:17:17.221Z [ERROR] ⚠️  High memory usage: 90.50%
2025-07-11T00:22:17.221Z [ERROR] ⚠️  High memory usage: 93.79%
2025-07-11T00:27:17.221Z [ERROR] ⚠️  High memory usage: 94.17%
2025-07-11T00:32:17.222Z [ERROR] ⚠️  High memory usage: 94.10%
2025-07-11T00:47:17.224Z [ERROR] ⚠️  High memory usage: 91.04%
2025-07-11T00:52:17.224Z [ERROR] ⚠️  High memory usage: 91.40%
2025-07-11T00:57:17.225Z [ERROR] ⚠️  High memory usage: 92.33%
2025-07-11T01:12:17.227Z [ERROR] ⚠️  High memory usage: 90.39%
2025-07-11T01:17:17.228Z [ERROR] ⚠️  High memory usage: 90.35%
2025-07-11T01:22:17.228Z [ERROR] ⚠️  High memory usage: 92.31%
2025-07-11T01:27:17.228Z [ERROR] ⚠️  High memory usage: 93.36%
2025-07-11T01:42:17.230Z [ERROR] ⚠️  High memory usage: 90.93%
2025-07-11T01:47:17.231Z [ERROR] ⚠️  High memory usage: 92.09%
2025-07-11T01:52:17.231Z [ERROR] ⚠️  High memory usage: 93.21%
2025-07-11T02:07:17.233Z [ERROR] ⚠️  High memory usage: 90.17%
2025-07-11T02:12:17.241Z [ERROR] ⚠️  High memory usage: 91.39%
2025-07-11T02:17:17.241Z [ERROR] ⚠️  High memory usage: 92.40%
2025-07-11T02:22:17.241Z [ERROR] ⚠️  High memory usage: 93.98%
2025-07-11T02:37:17.242Z [ERROR] ⚠️  High memory usage: 91.65%
2025-07-11T02:42:17.242Z [ERROR] ⚠️  High memory usage: 91.75%
2025-07-11T02:47:17.242Z [ERROR] ⚠️  High memory usage: 92.37%
2025-07-11T03:02:17.243Z [ERROR] ⚠️  High memory usage: 93.22%
2025-07-11T03:24:19.145Z [ERROR] [FFMPEG_STDERR] be652210-3216-4893-96cc-d87dc163237d: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/52wf-rete-q303-98eg-8312: Connection reset by peer
2025-07-11T03:24:19.187Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream be652210-3216-4893-96cc-d87dc163237d
2025-07-11T03:32:17.247Z [ERROR] ⚠️  High memory usage: 91.43%
2025-07-11T03:43:11.873Z [ERROR] [FFMPEG_STDERR] 65dd1f3d-eb1a-495c-a804-b5471b53112d: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/7wgq-svse-mxj4-ttra-258p: Broken pipe
2025-07-11T03:43:11.896Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 65dd1f3d-eb1a-495c-a804-b5471b53112d
2025-07-11T03:47:17.248Z [ERROR] ⚠️  High memory usage: 91.69%
2025-07-11T03:52:17.249Z [ERROR] ⚠️  High memory usage: 91.83%
2025-07-11T03:57:17.250Z [ERROR] ⚠️  High memory usage: 92.04%
2025-07-11T04:02:17.250Z [ERROR] ⚠️  High memory usage: 93.24%
2025-07-11T04:07:17.250Z [ERROR] ⚠️  High memory usage: 91.27%
2025-07-11T04:12:17.250Z [ERROR] ⚠️  High memory usage: 91.90%
2025-07-11T04:17:17.250Z [ERROR] ⚠️  High memory usage: 91.95%
2025-07-11T04:20:28.739Z [ERROR] [FFMPEG_STDERR] 3383a90f-6927-4692-b610-d1df58f43f09: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/g7x5-6epf-ug1h-ybq7-d2ct: Broken pipe
2025-07-11T04:20:28.767Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 3383a90f-6927-4692-b610-d1df58f43f09
2025-07-11T04:22:17.250Z [ERROR] ⚠️  High memory usage: 91.27%
2025-07-11T04:27:17.250Z [ERROR] ⚠️  High memory usage: 93.04%
2025-07-11T04:32:17.250Z [ERROR] ⚠️  High memory usage: 92.13%
2025-07-11T04:37:17.251Z [ERROR] ⚠️  High memory usage: 93.38%
2025-07-11T04:42:17.251Z [ERROR] ⚠️  High memory usage: 91.73%
2025-07-11T04:47:17.252Z [ERROR] ⚠️  High memory usage: 93.49%
2025-07-11T05:07:17.252Z [ERROR] ⚠️  High memory usage: 90.28%
2025-07-11T05:12:17.252Z [ERROR] ⚠️  High memory usage: 90.93%
2025-07-11T05:32:17.253Z [ERROR] ⚠️  High memory usage: 90.84%
2025-07-11T05:37:17.253Z [ERROR] ⚠️  High memory usage: 91.91%
2025-07-11T05:42:17.253Z [ERROR] ⚠️  High memory usage: 92.64%
2025-07-11T06:02:17.254Z [ERROR] ⚠️  High memory usage: 91.31%
2025-07-11T06:07:17.253Z [ERROR] ⚠️  High memory usage: 91.99%
2025-07-11T06:12:17.254Z [ERROR] ⚠️  High memory usage: 92.46%
2025-07-11T06:27:17.254Z [ERROR] ⚠️  High memory usage: 90.51%
2025-07-11T06:32:17.254Z [ERROR] ⚠️  High memory usage: 91.44%
2025-07-11T06:37:17.254Z [ERROR] ⚠️  High memory usage: 92.45%
2025-07-11T06:52:17.256Z [ERROR] ⚠️  High memory usage: 90.16%
2025-07-11T06:57:17.255Z [ERROR] ⚠️  High memory usage: 90.34%
2025-07-11T07:02:17.255Z [ERROR] ⚠️  High memory usage: 92.01%
2025-07-11T07:07:17.255Z [ERROR] ⚠️  High memory usage: 92.46%
2025-07-11T07:27:17.256Z [ERROR] ⚠️  High memory usage: 91.83%
2025-07-11T07:32:17.256Z [ERROR] ⚠️  High memory usage: 92.59%
2025-07-11T07:52:17.258Z [ERROR] ⚠️  High memory usage: 91.78%
2025-07-11T07:57:17.258Z [ERROR] ⚠️  High memory usage: 91.37%
2025-07-11T08:02:17.259Z [ERROR] ⚠️  High memory usage: 91.88%
2025-07-11T08:07:17.260Z [ERROR] ⚠️  High memory usage: 92.07%
2025-07-11T08:12:17.260Z [ERROR] ⚠️  High memory usage: 92.79%
2025-07-11T08:17:17.260Z [ERROR] ⚠️  High memory usage: 92.62%
2025-07-11T08:22:17.261Z [ERROR] ⚠️  High memory usage: 93.60%
2025-07-11T08:27:17.262Z [ERROR] ⚠️  High memory usage: 93.64%
2025-07-11T08:32:17.263Z [ERROR] ⚠️  High memory usage: 90.85%
2025-07-11T08:37:17.263Z [ERROR] ⚠️  High memory usage: 93.65%
2025-07-11T08:47:17.265Z [ERROR] ⚠️  High memory usage: 92.11%
2025-07-11T08:52:17.263Z [ERROR] ⚠️  High memory usage: 94.20%
2025-07-11T09:02:17.264Z [ERROR] ⚠️  High memory usage: 92.76%
2025-07-11T09:22:17.265Z [ERROR] ⚠️  High memory usage: 90.22%
2025-07-11T09:27:17.266Z [ERROR] ⚠️  High memory usage: 91.11%
2025-07-11T09:42:17.267Z [ERROR] ⚠️  High memory usage: 90.26%
2025-07-11T09:47:17.267Z [ERROR] ⚠️  High memory usage: 91.63%
2025-07-11T09:52:17.267Z [ERROR] ⚠️  High memory usage: 92.50%
2025-07-11T10:12:17.267Z [ERROR] ⚠️  High memory usage: 90.49%
2025-07-11T10:17:17.267Z [ERROR] ⚠️  High memory usage: 92.27%
2025-07-11T10:22:17.267Z [ERROR] ⚠️  High memory usage: 93.39%
2025-07-11T10:37:17.268Z [ERROR] ⚠️  High memory usage: 90.23%
2025-07-11T10:42:17.268Z [ERROR] ⚠️  High memory usage: 90.61%
2025-07-11T10:47:17.269Z [ERROR] ⚠️  High memory usage: 92.28%
2025-07-11T10:52:17.269Z [ERROR] ⚠️  High memory usage: 93.16%
2025-07-11T11:12:17.271Z [ERROR] ⚠️  High memory usage: 91.14%
2025-07-11T11:17:17.272Z [ERROR] ⚠️  High memory usage: 91.90%
2025-07-11T11:22:17.271Z [ERROR] ⚠️  High memory usage: 93.45%
2025-07-11T11:37:17.274Z [ERROR] ⚠️  High memory usage: 90.35%
2025-07-11T11:42:17.274Z [ERROR] ⚠️  High memory usage: 91.30%
2025-07-11T11:47:17.274Z [ERROR] ⚠️  High memory usage: 92.39%
2025-07-11T12:07:17.276Z [ERROR] ⚠️  High memory usage: 90.39%
2025-07-11T12:12:17.276Z [ERROR] ⚠️  High memory usage: 91.31%
2025-07-11T12:17:17.277Z [ERROR] ⚠️  High memory usage: 92.49%
2025-07-11T12:27:17.277Z [ERROR] ⚠️  High memory usage: 90.90%
2025-07-11T12:32:17.278Z [ERROR] ⚠️  High memory usage: 92.44%
2025-07-11T12:37:17.279Z [ERROR] ⚠️  High memory usage: 93.24%
2025-07-11T12:39:43.033Z [ERROR] [FFMPEG_STDERR] d1daeb9c-56d4-4fa0-ad17-e9b2855a3d5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/syc8-cwc2-drah-rpmd-279y: Broken pipe
2025-07-11T12:39:43.419Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream d1daeb9c-56d4-4fa0-ad17-e9b2855a3d5b
2025-07-11T12:52:17.279Z [ERROR] ⚠️  High memory usage: 90.07%
2025-07-11T12:57:17.280Z [ERROR] ⚠️  High memory usage: 90.77%
2025-07-11T13:02:17.279Z [ERROR] ⚠️  High memory usage: 90.25%
2025-07-11T13:07:17.280Z [ERROR] ⚠️  High memory usage: 93.73%
2025-07-11T13:17:17.281Z [ERROR] ⚠️  High memory usage: 91.26%
2025-07-11T13:27:17.280Z [ERROR] ⚠️  High memory usage: 91.91%
2025-07-11T13:52:17.283Z [ERROR] ⚠️  High memory usage: 90.41%
2025-07-11T13:57:17.284Z [ERROR] ⚠️  High memory usage: 91.38%
2025-07-11T13:59:59.563Z [ERROR] [FFMPEG_STDERR] d1daeb9c-56d4-4fa0-ad17-e9b2855a3d5b: Error writing trailer of rtmp://a.rtmp.youtube.com/live2/syc8-cwc2-drah-rpmd-279y: Broken pipe
2025-07-11T13:59:59.584Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream d1daeb9c-56d4-4fa0-ad17-e9b2855a3d5b
2025-07-11T14:22:17.286Z [ERROR] ⚠️  High memory usage: 90.44%
2025-07-11T14:27:17.286Z [ERROR] ⚠️  High memory usage: 91.69%
2025-07-11T14:52:17.289Z [ERROR] ⚠️  High memory usage: 90.29%
2025-07-11T14:57:17.288Z [ERROR] ⚠️  High memory usage: 91.72%
2025-07-11T15:22:17.290Z [ERROR] ⚠️  High memory usage: 90.94%
2025-07-11T15:42:17.293Z [ERROR] ⚠️  High memory usage: 90.37%
2025-07-11T15:47:17.293Z [ERROR] ⚠️  High memory usage: 91.23%
2025-07-11T16:17:17.296Z [ERROR] ⚠️  High memory usage: 91.16%
2025-07-11T16:42:17.297Z [ERROR] ⚠️  High memory usage: 90.68%
2025-07-11T16:47:17.297Z [ERROR] ⚠️  High memory usage: 91.06%
2025-07-11T17:07:17.299Z [ERROR] ⚠️  High memory usage: 90.09%
2025-07-11T17:12:17.300Z [ERROR] ⚠️  High memory usage: 91.53%
2025-07-11T17:37:17.303Z [ERROR] ⚠️  High memory usage: 90.60%
2025-07-11T17:42:17.303Z [ERROR] ⚠️  High memory usage: 91.13%
2025-07-11T18:07:17.304Z [ERROR] ⚠️  High memory usage: 90.38%
2025-07-11T18:12:17.305Z [ERROR] ⚠️  High memory usage: 91.30%
2025-07-11T18:37:17.307Z [ERROR] ⚠️  High memory usage: 91.38%
2025-07-11T19:02:17.307Z [ERROR] ⚠️  High memory usage: 90.49%
2025-07-11T19:07:17.308Z [ERROR] ⚠️  High memory usage: 91.58%
2025-07-11T19:32:17.308Z [ERROR] ⚠️  High memory usage: 90.21%
2025-07-11T19:37:17.308Z [ERROR] ⚠️  High memory usage: 91.26%
2025-07-11T20:02:17.311Z [ERROR] ⚠️  High memory usage: 90.96%
2025-07-11T20:07:17.310Z [ERROR] ⚠️  High memory usage: 91.40%
2025-07-11T20:27:17.313Z [ERROR] ⚠️  High memory usage: 90.91%
2025-07-11T20:32:17.313Z [ERROR] ⚠️  High memory usage: 91.78%
2025-07-11T20:52:17.313Z [ERROR] ⚠️  High memory usage: 90.12%
2025-07-11T20:57:17.314Z [ERROR] ⚠️  High memory usage: 91.19%
2025-07-11T21:22:17.315Z [ERROR] ⚠️  High memory usage: 90.61%
2025-07-11T21:27:17.316Z [ERROR] ⚠️  High memory usage: 91.74%
2025-07-11T21:52:17.317Z [ERROR] ⚠️  High memory usage: 91.16%
2025-07-11T21:57:17.317Z [ERROR] ⚠️  High memory usage: 91.19%
2025-07-11T22:22:17.321Z [ERROR] ⚠️  High memory usage: 90.56%
2025-07-11T22:27:17.322Z [ERROR] ⚠️  High memory usage: 91.91%
2025-07-11T22:47:17.324Z [ERROR] ⚠️  High memory usage: 90.08%
2025-07-11T22:52:17.324Z [ERROR] ⚠️  High memory usage: 91.31%
2025-07-11T23:22:17.329Z [ERROR] ⚠️  High memory usage: 91.15%
2025-07-11T23:47:17.331Z [ERROR] ⚠️  High memory usage: 90.02%
2025-07-11T23:52:17.331Z [ERROR] ⚠️  High memory usage: 90.95%
2025-07-12T00:17:17.333Z [ERROR] ⚠️  High memory usage: 90.89%
2025-07-12T00:22:17.333Z [ERROR] ⚠️  High memory usage: 91.53%
2025-07-12T00:47:17.333Z [ERROR] ⚠️  High memory usage: 90.43%
2025-07-12T01:17:17.337Z [ERROR] ⚠️  High memory usage: 91.06%
2025-07-12T01:42:17.339Z [ERROR] ⚠️  High memory usage: 90.39%
2025-07-12T01:47:17.340Z [ERROR] ⚠️  High memory usage: 91.26%
2025-07-12T02:07:17.341Z [ERROR] ⚠️  High memory usage: 90.15%
2025-07-12T02:12:17.341Z [ERROR] ⚠️  High memory usage: 91.04%
2025-07-12T02:17:17.342Z [ERROR] ⚠️  High memory usage: 91.94%
2025-07-12T02:45:42.462Z [ERROR] ❌ Dragonfly connection failed: Command timed out
2025-07-12T02:45:42.480Z [ERROR] ❌ Dragonfly main client error: connect ECONNREFUSED 127.0.0.1:6379
2025-07-12T02:45:42.480Z [ERROR] ❌ Dragonfly main client error: connect ECONNREFUSED 127.0.0.1:6379
2025-07-12T02:45:42.564Z [ERROR] ❌ Dragonfly main client error: connect ECONNREFUSED 127.0.0.1:6379
2025-07-12T02:45:42.566Z [ERROR] ❌ Dragonfly main client error: connect ECONNREFUSED 127.0.0.1:6379
2025-07-12T02:45:42.667Z [ERROR] ❌ Dragonfly main client error: connect ECONNREFUSED 127.0.0.1:6379
2025-07-12T02:45:42.668Z [ERROR] ❌ Dragonfly main client error: connect ECONNREFUSED 127.0.0.1:6379
2025-07-12T02:45:42.824Z [ERROR] ❌ Dragonfly main client error: connect ECONNREFUSED 127.0.0.1:6379
2025-07-12T02:45:42.826Z [ERROR] ❌ Dragonfly connection failed: Reached the max retries per request limit (which is 3). Refer to "maxRetriesPerRequest" option for details.
2025-07-12T02:45:42.826Z [ERROR] ❌ Dragonfly main client error: connect ECONNREFUSED 127.0.0.1:6379
2025-07-12T02:45:43.040Z [ERROR] ❌ Dragonfly main client error: connect ECONNREFUSED 127.0.0.1:6379
2025-07-12T02:45:43.041Z [ERROR] ❌ Dragonfly main client error: connect ECONNREFUSED 127.0.0.1:6379
2025-07-12T02:45:43.303Z [ERROR] ❌ Dragonfly main client error: connect ECONNREFUSED 127.0.0.1:6379
2025-07-12T02:45:43.304Z [ERROR] ❌ Dragonfly main client error: connect ECONNREFUSED 127.0.0.1:6379
2025-07-12T02:45:43.617Z [ERROR] ❌ Dragonfly main client error: connect ECONNREFUSED 127.0.0.1:6379
2025-07-12T02:45:43.618Z [ERROR] ❌ Dragonfly main client error: connect ECONNREFUSED 127.0.0.1:6379
2025-07-12T02:45:57.607Z [ERROR] ❌ Dragonfly main client error: connect ECONNREFUSED 127.0.0.1:6379
2025-07-12T02:45:57.609Z [ERROR] ❌ Dragonfly main client error: connect ECONNREFUSED 127.0.0.1:6379
2025-07-12T02:45:57.685Z [ERROR] ❌ Dragonfly main client error: connect ECONNREFUSED 127.0.0.1:6379
2025-07-12T02:45:57.690Z [ERROR] ❌ Dragonfly main client error: connect ECONNREFUSED 127.0.0.1:6379
2025-07-12T02:45:57.802Z [ERROR] ❌ Dragonfly main client error: connect ECONNREFUSED 127.0.0.1:6379
2025-07-12T02:45:57.803Z [ERROR] ❌ Dragonfly main client error: connect ECONNREFUSED 127.0.0.1:6379
2025-07-12T02:45:57.965Z [ERROR] ❌ Dragonfly main client error: connect ECONNREFUSED 127.0.0.1:6379
2025-07-12T02:45:57.967Z [ERROR] ❌ Dragonfly connection failed: Reached the max retries per request limit (which is 3). Refer to "maxRetriesPerRequest" option for details.
2025-07-12T02:45:57.968Z [ERROR] ❌ Dragonfly main client error: connect ECONNREFUSED 127.0.0.1:6379
2025-07-12T02:45:57.969Z [ERROR] ❌ Dragonfly connection failed: Reached the max retries per request limit (which is 3). Refer to "maxRetriesPerRequest" option for details.
2025-07-12T02:45:58.175Z [ERROR] ❌ Dragonfly main client error: connect ECONNREFUSED 127.0.0.1:6379
2025-07-12T02:45:58.176Z [ERROR] ❌ Dragonfly main client error: connect ECONNREFUSED 127.0.0.1:6379
2025-07-12T02:45:58.433Z [ERROR] ❌ Dragonfly main client error: connect ECONNREFUSED 127.0.0.1:6379
2025-07-12T02:45:58.434Z [ERROR] ❌ Dragonfly main client error: connect ECONNREFUSED 127.0.0.1:6379
2025-07-12T02:45:58.753Z [ERROR] ❌ Dragonfly main client error: connect ECONNREFUSED 127.0.0.1:6379
2025-07-12T02:45:58.753Z [ERROR] ❌ Dragonfly main client error: connect ECONNREFUSED 127.0.0.1:6379
2025-07-12T02:45:59.117Z [ERROR] ❌ Dragonfly main client error: connect ECONNREFUSED 127.0.0.1:6379
2025-07-12T02:45:59.118Z [ERROR] ❌ Dragonfly main client error: connect ECONNREFUSED 127.0.0.1:6379
2025-07-12T02:45:59.524Z [ERROR] ❌ Dragonfly main client error: connect ECONNREFUSED 127.0.0.1:6379
2025-07-12T02:45:59.525Z [ERROR] ❌ Dragonfly main client error: connect ECONNREFUSED 127.0.0.1:6379
2025-07-12T02:46:16.972Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:46:16.974Z [ERROR] ❌ Dragonfly connection failed: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:46:16.978Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:46:16.979Z [ERROR] ❌ Dragonfly connection failed: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:46:17.053Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:46:17.063Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:46:17.171Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:46:17.255Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:46:17.334Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:46:17.427Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:46:17.552Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:46:17.644Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:46:17.816Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:46:17.910Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:46:18.129Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:46:18.222Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:46:18.497Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:46:18.591Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:46:18.917Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:46:19.012Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:46:19.385Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:46:19.480Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:46:19.900Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:46:19.992Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:46:20.459Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:46:20.554Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:46:21.070Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:46:21.162Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:46:21.737Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:46:21.832Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:46:22.455Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:46:22.547Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:46:23.216Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:46:23.311Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:46:24.030Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:46:24.124Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:47:43.034Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:47:43.035Z [ERROR] ❌ Dragonfly connection failed: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:47:43.044Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:47:43.044Z [ERROR] ❌ Dragonfly connection failed: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:47:43.162Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:47:43.164Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:47:43.284Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:47:43.285Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:47:43.456Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:47:43.457Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:47:43.676Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:47:43.677Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:47:43.942Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:47:43.943Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:47:44.256Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:47:44.258Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:47:44.629Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:47:44.633Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:47:45.047Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:47:45.056Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:47:45.519Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:47:45.520Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:47:46.030Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:47:46.031Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:47:46.600Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:47:46.601Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:47:47.209Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:47:47.211Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:47:47.881Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:47:47.882Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:49:40.578Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:49:40.579Z [ERROR] ❌ Dragonfly connection failed: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:49:40.581Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:49:40.582Z [ERROR] ❌ Dragonfly connection failed: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:49:40.708Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:49:40.709Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:49:40.824Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:49:40.825Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:49:40.992Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:49:40.993Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:49:41.209Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:49:41.210Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:49:41.472Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:49:41.473Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:49:41.785Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:49:41.785Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:49:42.159Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:49:42.161Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:49:42.584Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:49:42.586Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:49:43.046Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:49:43.047Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:49:43.556Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:49:43.557Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:49:44.127Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:49:44.128Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:49:44.736Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:49:44.738Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:49:45.403Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T02:49:45.405Z [ERROR] ❌ Dragonfly main client error: WRONGPASS invalid username-password pair or user is disabled.
2025-07-12T04:16:01.345Z [ERROR] -----------------------------------
2025-07-12T04:16:01.347Z [ERROR] UNCAUGHT EXCEPTION: Error: listen EADDRINUSE: address already in use 0.0.0.0:7575
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at doListen (node:net:2069:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '0.0.0.0',
  port: 7575
}
2025-07-12T04:16:01.348Z [ERROR] -----------------------------------
2025-07-12T04:25:44.971Z [ERROR] -----------------------------------
2025-07-12T04:25:44.973Z [ERROR] UNCAUGHT EXCEPTION: Error: listen EADDRINUSE: address already in use 0.0.0.0:7575
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at doListen (node:net:2069:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '0.0.0.0',
  port: 7575
}
2025-07-12T04:25:44.973Z [ERROR] -----------------------------------
2025-07-12T04:34:47.400Z [ERROR] Error fetching current UTC time: connect ETIMEDOUT **************:443
2025-07-12T05:28:22.977Z [ERROR] Error fetching current UTC time: connect ETIMEDOUT **************:443
2025-07-12T05:41:30.559Z [ERROR] Error fetching current UTC time: connect ETIMEDOUT **************:443
2025-07-12T05:53:44.823Z [ERROR] Error fetching current UTC time: connect ETIMEDOUT **************:443
2025-07-12T05:54:03.422Z [ERROR] Midtrans create transaction error: MidtransError: Midtrans API is returning API error. HTTP status code: 400. API response: {"error_messages":["transaction_details.gross_amount is required","transaction_details.gross_amount is not a number","transaction_details.order_id is required"]}
    at C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\midtrans-client\lib\httpClient.js:85:13
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5) {
  httpStatusCode: 400,
  ApiResponse: {
    error_messages: [
      'transaction_details.gross_amount is required',
      'transaction_details.gross_amount is not a number',
      'transaction_details.order_id is required'
    ]
  },
  rawHttpClientData: {
    status: 400,
    statusText: 'Bad Request',
    headers: {
      date: 'Sat, 12 Jul 2025 05:54:04 GMT',
      'content-type': 'application/json; charset=utf-8',
      'transfer-encoding': 'chunked',
      connection: 'keep-alive',
      'cf-ray': '95de48a87a5b3d81-SIN',
      vary: 'Accept,Accept-Encoding',
      'x-request-id': '150cbf1c-cb55-44ea-8d61-0895e2d60eb1',
      'referrer-policy': 'origin-when-cross-origin',
      'x-content-type-options': 'nosniff',
      'x-download-options': 'noopen',
      'x-frame-options': 'SAMEORIGIN',
      'x-permitted-cross-domain-policies': 'none',
      'x-xss-protection': '1; mode=block',
      'x-envoy-upstream-service-time': '57',
      'strict-transport-security': 'max-age=63072000; includeSubDomains; preload',
      'cf-cache-status': 'DYNAMIC',
      'report-to': '{"endpoints":[{"url":"https:\\/\\/a.nel.cloudflare.com\\/report\\/v4?s=PVrNYzXs%2FTpq%2F7FYYdUCalapWPKoYmH67xSJ6BePeOH4NU6bU0ncgeoW5gm%2FI7zn7JVMJyTs770%2BoDT371FzYyrbyT9Qj2GLDgRE55zdTmSXw8bMCOLcvOJif%2FZcZOknR5Q%3D"}],"group":"cf-nel","max_age":604800}',
      nel: '{"success_fraction":0,"report_to":"cf-nel","max_age":604800}',
      server: 'cloudflare',
      'server-timing': 'cfL4;desc="?proto=TCP&rtt=21591&min_rtt=21142&rtt_var=8249&sent=5&recv=5&lost=0&retrans=0&sent_bytes=2829&recv_bytes=931&delivery_rate=198656&cwnd=252&unsent_bytes=0&cid=7da9be21d676de56&ts=201&x=0"'
    },
    config: {
      transitional: [Object],
      adapter: [Function: httpAdapter],
      transformRequest: [Array],
      transformResponse: [Array],
      timeout: 0,
      xsrfCookieName: 'XSRF-TOKEN',
      xsrfHeaderName: 'X-XSRF-TOKEN',
      maxContentLength: -1,
      maxBodyLength: -1,
      validateStatus: [Function: validateStatus],
      headers: [Object],
      method: 'post',
      url: 'https://app.midtrans.com/snap/v1/transactions',
      data: '{"transaction_details":{},"credit_card":{"secure":true},"callbacks":{"finish":"https://streamonpod.com/payment/finish"},"custom_expiry":{"expiry_duration":1440,"unit":"minute"}}',
      params: {},
      auth: [Object]
    },
    request: ClientRequest {
      _events: [Object: null prototype],
      _eventsCount: 7,
      _maxListeners: undefined,
      outputData: [],
      outputSize: 0,
      writable: true,
      destroyed: true,
      _last: false,
      chunkedEncoding: false,
      shouldKeepAlive: true,
      maxRequestsOnConnectionReached: false,
      _defaultKeepAlive: true,
      useChunkedEncodingByDefault: true,
      sendDate: false,
      _removedConnection: false,
      _removedContLen: false,
      _removedTE: false,
      strictContentLength: false,
      _contentLength: 177,
      _hasBody: true,
      _trailer: '',
      finished: true,
      _headerSent: true,
      _closed: true,
      socket: [TLSSocket],
      _header: 'POST /snap/v1/transactions HTTP/1.1\r\n' +
        'Accept: application/json\r\n' +
        'Content-Type: application/json\r\n' +
        'user-agent: midtransclient-nodejs/1.4.2\r\n' +
        'Content-Length: 177\r\n' +
        'Host: app.midtrans.com\r\n' +
        'Authorization: Basic TWlkLXNlcnZlci03UU45cm5KVk5nOUFob1ZWQ3FDSF9fYzQ6\r\n' +
        'Connection: keep-alive\r\n' +
        '\r\n',
      _keepAliveTimeout: 0,
      _onPendingData: [Function: nop],
      agent: [Agent],
      socketPath: undefined,
      method: 'POST',
      maxHeaderSize: undefined,
      insecureHTTPParser: undefined,
      joinDuplicateHeaders: undefined,
      path: '/snap/v1/transactions',
      _ended: true,
      res: [IncomingMessage],
      aborted: false,
      timeoutCb: null,
      upgradeOrConnect: false,
      parser: null,
      maxHeadersCount: null,
      reusedSocket: false,
      host: 'app.midtrans.com',
      protocol: 'https:',
      _redirectable: [Writable],
      [Symbol(kCapture)]: false,
      [Symbol(kBytesWritten)]: 0,
      [Symbol(kNeedDrain)]: false,
      [Symbol(corked)]: 0,
      [Symbol(kOutHeaders)]: [Object: null prototype],
      [Symbol(errored)]: null,
      [Symbol(kHighWaterMark)]: 16384,
      [Symbol(kRejectNonStandardBodyWrites)]: false,
      [Symbol(kUniqueHeaders)]: null
    },
    data: { error_messages: [Array] }
  }
}
2025-07-12T05:54:03.424Z [ERROR] Renewal payment error: TypeError: Transaction.updateMidtransData is not a function
    at C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\routes\subscription.js:672:23
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-07-12T05:54:20.788Z [ERROR] Midtrans create transaction error: MidtransError: Midtrans API is returning API error. HTTP status code: 400. API response: {"error_messages":["transaction_details.gross_amount is required","transaction_details.gross_amount is not a number","transaction_details.order_id is required"]}
    at C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\midtrans-client\lib\httpClient.js:85:13
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5) {
  httpStatusCode: 400,
  ApiResponse: {
    error_messages: [
      'transaction_details.gross_amount is required',
      'transaction_details.gross_amount is not a number',
      'transaction_details.order_id is required'
    ]
  },
  rawHttpClientData: {
    status: 400,
    statusText: 'Bad Request',
    headers: {
      date: 'Sat, 12 Jul 2025 05:54:21 GMT',
      'content-type': 'application/json; charset=utf-8',
      'transfer-encoding': 'chunked',
      connection: 'keep-alive',
      'cf-ray': '95de491548b50387-HKG',
      vary: 'Accept,Accept-Encoding',
      'x-request-id': '15f2eb7b-4d50-47f6-bba9-234504d98a69',
      'referrer-policy': 'origin-when-cross-origin',
      'x-content-type-options': 'nosniff',
      'x-download-options': 'noopen',
      'x-frame-options': 'SAMEORIGIN',
      'x-permitted-cross-domain-policies': 'none',
      'x-xss-protection': '1; mode=block',
      'x-envoy-upstream-service-time': '27',
      'strict-transport-security': 'max-age=63072000; includeSubDomains; preload',
      'cf-cache-status': 'DYNAMIC',
      'report-to': '{"endpoints":[{"url":"https:\\/\\/a.nel.cloudflare.com\\/report\\/v4?s=%2BF2QfivOnaHtf8750uCBLL3OHsJDEVGFjUZX7c33d5JpN8MGmZV9NxId8oLj0JtHrBdjooeoDOWla5B2kGqcTQKlVy%2FhOx8otErd5AmOIFEBufFEuP5QNk8ANJlS4akWQKc%3D"}],"group":"cf-nel","max_age":604800}',
      nel: '{"success_fraction":0,"report_to":"cf-nel","max_age":604800}',
      server: 'cloudflare',
      'server-timing': 'cfL4;desc="?proto=TCP&rtt=53685&min_rtt=53370&rtt_var=20239&sent=5&recv=5&lost=0&retrans=0&sent_bytes=2828&recv_bytes=931&delivery_rate=78695&cwnd=252&unsent_bytes=0&cid=81a1bdd2864bdefd&ts=163&x=0"'
    },
    config: {
      transitional: [Object],
      adapter: [Function: httpAdapter],
      transformRequest: [Array],
      transformResponse: [Array],
      timeout: 0,
      xsrfCookieName: 'XSRF-TOKEN',
      xsrfHeaderName: 'X-XSRF-TOKEN',
      maxContentLength: -1,
      maxBodyLength: -1,
      validateStatus: [Function: validateStatus],
      headers: [Object],
      method: 'post',
      url: 'https://app.midtrans.com/snap/v1/transactions',
      data: '{"transaction_details":{},"credit_card":{"secure":true},"callbacks":{"finish":"https://streamonpod.com/payment/finish"},"custom_expiry":{"expiry_duration":1440,"unit":"minute"}}',
      params: {},
      auth: [Object]
    },
    request: ClientRequest {
      _events: [Object: null prototype],
      _eventsCount: 7,
      _maxListeners: undefined,
      outputData: [],
      outputSize: 0,
      writable: true,
      destroyed: true,
      _last: false,
      chunkedEncoding: false,
      shouldKeepAlive: true,
      maxRequestsOnConnectionReached: false,
      _defaultKeepAlive: true,
      useChunkedEncodingByDefault: true,
      sendDate: false,
      _removedConnection: false,
      _removedContLen: false,
      _removedTE: false,
      strictContentLength: false,
      _contentLength: 177,
      _hasBody: true,
      _trailer: '',
      finished: true,
      _headerSent: true,
      _closed: true,
      socket: [TLSSocket],
      _header: 'POST /snap/v1/transactions HTTP/1.1\r\n' +
        'Accept: application/json\r\n' +
        'Content-Type: application/json\r\n' +
        'user-agent: midtransclient-nodejs/1.4.2\r\n' +
        'Content-Length: 177\r\n' +
        'Host: app.midtrans.com\r\n' +
        'Authorization: Basic TWlkLXNlcnZlci03UU45cm5KVk5nOUFob1ZWQ3FDSF9fYzQ6\r\n' +
        'Connection: keep-alive\r\n' +
        '\r\n',
      _keepAliveTimeout: 0,
      _onPendingData: [Function: nop],
      agent: [Agent],
      socketPath: undefined,
      method: 'POST',
      maxHeaderSize: undefined,
      insecureHTTPParser: undefined,
      joinDuplicateHeaders: undefined,
      path: '/snap/v1/transactions',
      _ended: true,
      res: [IncomingMessage],
      aborted: false,
      timeoutCb: null,
      upgradeOrConnect: false,
      parser: null,
      maxHeadersCount: null,
      reusedSocket: false,
      host: 'app.midtrans.com',
      protocol: 'https:',
      _redirectable: [Writable],
      [Symbol(kCapture)]: false,
      [Symbol(kBytesWritten)]: 0,
      [Symbol(kNeedDrain)]: false,
      [Symbol(corked)]: 0,
      [Symbol(kOutHeaders)]: [Object: null prototype],
      [Symbol(errored)]: null,
      [Symbol(kHighWaterMark)]: 16384,
      [Symbol(kRejectNonStandardBodyWrites)]: false,
      [Symbol(kUniqueHeaders)]: null
    },
    data: { error_messages: [Array] }
  }
}
2025-07-12T05:54:20.791Z [ERROR] Renewal payment error: TypeError: Transaction.updateMidtransData is not a function
    at C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\routes\subscription.js:672:23
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-07-12T06:04:20.394Z [ERROR] Midtrans create transaction error: MidtransError: Midtrans API is returning API error. HTTP status code: 400. API response: {"error_messages":["transaction_details.gross_amount is required","transaction_details.gross_amount is not a number","transaction_details.order_id is required"]}
    at C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\midtrans-client\lib\httpClient.js:85:13
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5) {
  httpStatusCode: 400,
  ApiResponse: {
    error_messages: [
      'transaction_details.gross_amount is required',
      'transaction_details.gross_amount is not a number',
      'transaction_details.order_id is required'
    ]
  },
  rawHttpClientData: {
    status: 400,
    statusText: 'Bad Request',
    headers: {
      date: 'Sat, 12 Jul 2025 06:04:21 GMT',
      'content-type': 'application/json; charset=utf-8',
      'transfer-encoding': 'chunked',
      connection: 'keep-alive',
      'cf-ray': '95de57b8f80bd43a-SIN',
      vary: 'Accept,Accept-Encoding',
      'x-request-id': 'f10627ac-22a7-4b60-b65a-14e841a7cbc5',
      'referrer-policy': 'origin-when-cross-origin',
      'x-content-type-options': 'nosniff',
      'x-download-options': 'noopen',
      'x-frame-options': 'SAMEORIGIN',
      'x-permitted-cross-domain-policies': 'none',
      'x-xss-protection': '1; mode=block',
      'x-envoy-upstream-service-time': '30',
      'strict-transport-security': 'max-age=63072000; includeSubDomains; preload',
      'cf-cache-status': 'DYNAMIC',
      'report-to': '{"endpoints":[{"url":"https:\\/\\/a.nel.cloudflare.com\\/report\\/v4?s=Z4%2F8Nk5L16q%2BqvArqREcS4wM4faK0kgNpSjNFFcKHxAINScgqj3OYM4igGkOyTB5qsqIYGkvWd4t7qSe3b%2BuW7WtRK%2B6Kh%2Fh%2FXVmSdsF%2BRh6ncsMOeC2XxEpzY%2F7yx%2FfOXo%3D"}],"group":"cf-nel","max_age":604800}',
      nel: '{"success_fraction":0,"report_to":"cf-nel","max_age":604800}',
      server: 'cloudflare',
      'server-timing': 'cfL4;desc="?proto=TCP&rtt=28872&min_rtt=28246&rtt_var=11845&sent=5&recv=5&lost=0&retrans=0&sent_bytes=2828&recv_bytes=931&delivery_rate=126277&cwnd=252&unsent_bytes=0&cid=30281a99ec0ee589&ts=137&x=0"'
    },
    config: {
      transitional: [Object],
      adapter: [Function: httpAdapter],
      transformRequest: [Array],
      transformResponse: [Array],
      timeout: 0,
      xsrfCookieName: 'XSRF-TOKEN',
      xsrfHeaderName: 'X-XSRF-TOKEN',
      maxContentLength: -1,
      maxBodyLength: -1,
      validateStatus: [Function: validateStatus],
      headers: [Object],
      method: 'post',
      url: 'https://app.midtrans.com/snap/v1/transactions',
      data: '{"transaction_details":{},"credit_card":{"secure":true},"callbacks":{"finish":"https://streamonpod.com/payment/finish"},"custom_expiry":{"expiry_duration":1440,"unit":"minute"}}',
      params: {},
      auth: [Object]
    },
    request: ClientRequest {
      _events: [Object: null prototype],
      _eventsCount: 7,
      _maxListeners: undefined,
      outputData: [],
      outputSize: 0,
      writable: true,
      destroyed: true,
      _last: false,
      chunkedEncoding: false,
      shouldKeepAlive: true,
      maxRequestsOnConnectionReached: false,
      _defaultKeepAlive: true,
      useChunkedEncodingByDefault: true,
      sendDate: false,
      _removedConnection: false,
      _removedContLen: false,
      _removedTE: false,
      strictContentLength: false,
      _contentLength: 177,
      _hasBody: true,
      _trailer: '',
      finished: true,
      _headerSent: true,
      _closed: true,
      socket: [TLSSocket],
      _header: 'POST /snap/v1/transactions HTTP/1.1\r\n' +
        'Accept: application/json\r\n' +
        'Content-Type: application/json\r\n' +
        'user-agent: midtransclient-nodejs/1.4.2\r\n' +
        'Content-Length: 177\r\n' +
        'Host: app.midtrans.com\r\n' +
        'Authorization: Basic TWlkLXNlcnZlci03UU45cm5KVk5nOUFob1ZWQ3FDSF9fYzQ6\r\n' +
        'Connection: keep-alive\r\n' +
        '\r\n',
      _keepAliveTimeout: 0,
      _onPendingData: [Function: nop],
      agent: [Agent],
      socketPath: undefined,
      method: 'POST',
      maxHeaderSize: undefined,
      insecureHTTPParser: undefined,
      joinDuplicateHeaders: undefined,
      path: '/snap/v1/transactions',
      _ended: true,
      res: [IncomingMessage],
      aborted: false,
      timeoutCb: null,
      upgradeOrConnect: false,
      parser: null,
      maxHeadersCount: null,
      reusedSocket: false,
      host: 'app.midtrans.com',
      protocol: 'https:',
      _redirectable: [Writable],
      [Symbol(kCapture)]: false,
      [Symbol(kBytesWritten)]: 0,
      [Symbol(kNeedDrain)]: false,
      [Symbol(corked)]: 0,
      [Symbol(kOutHeaders)]: [Object: null prototype],
      [Symbol(errored)]: null,
      [Symbol(kHighWaterMark)]: 16384,
      [Symbol(kRejectNonStandardBodyWrites)]: false,
      [Symbol(kUniqueHeaders)]: null
    },
    data: { error_messages: [Array] }
  }
}
2025-07-12T06:04:40.555Z [ERROR] Midtrans create transaction error: MidtransError: Midtrans API is returning API error. HTTP status code: 400. API response: {"error_messages":["transaction_details.gross_amount is required","transaction_details.gross_amount is not a number","transaction_details.order_id is required"]}
    at C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\midtrans-client\lib\httpClient.js:85:13
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5) {
  httpStatusCode: 400,
  ApiResponse: {
    error_messages: [
      'transaction_details.gross_amount is required',
      'transaction_details.gross_amount is not a number',
      'transaction_details.order_id is required'
    ]
  },
  rawHttpClientData: {
    status: 400,
    statusText: 'Bad Request',
    headers: {
      date: 'Sat, 12 Jul 2025 06:04:41 GMT',
      'content-type': 'application/json; charset=utf-8',
      'transfer-encoding': 'chunked',
      connection: 'keep-alive',
      'cf-ray': '95de5835cf9fb1e9-HKG',
      vary: 'Accept,Accept-Encoding',
      'x-request-id': 'e6b59af1-cad1-4554-872a-14b937be779b',
      'referrer-policy': 'origin-when-cross-origin',
      'x-content-type-options': 'nosniff',
      'x-download-options': 'noopen',
      'x-frame-options': 'SAMEORIGIN',
      'x-permitted-cross-domain-policies': 'none',
      'x-xss-protection': '1; mode=block',
      'x-envoy-upstream-service-time': '27',
      'strict-transport-security': 'max-age=63072000; includeSubDomains; preload',
      'cf-cache-status': 'DYNAMIC',
      'report-to': '{"endpoints":[{"url":"https:\\/\\/a.nel.cloudflare.com\\/report\\/v4?s=urNjcDh1WZ69z5i0ik2EACCvNh2y%2FNjEIcB4QrgDrPsMNDFtzrI3uWBFaAdv5Mc3k6VWNQxJ02btpZCjaMxvBpu3gqAWXTPq2%2FOsE3Un7hhLO%2F0YFppH8ggWIR7QVaAj%2ByA%3D"}],"group":"cf-nel","max_age":604800}',
      nel: '{"success_fraction":0,"report_to":"cf-nel","max_age":604800}',
      server: 'cloudflare',
      'server-timing': 'cfL4;desc="?proto=TCP&rtt=56322&min_rtt=56041&rtt_var=21579&sent=5&recv=5&lost=0&retrans=0&sent_bytes=2828&recv_bytes=1186&delivery_rate=72047&cwnd=252&unsent_bytes=0&cid=077184319e88c55b&ts=322&x=0"'
    },
    config: {
      transitional: [Object],
      adapter: [Function: httpAdapter],
      transformRequest: [Array],
      transformResponse: [Array],
      timeout: 0,
      xsrfCookieName: 'XSRF-TOKEN',
      xsrfHeaderName: 'X-XSRF-TOKEN',
      maxContentLength: -1,
      maxBodyLength: -1,
      validateStatus: [Function: validateStatus],
      headers: [Object],
      method: 'post',
      url: 'https://app.midtrans.com/snap/v1/transactions',
      data: '{"transaction_details":{},"credit_card":{"secure":true},"callbacks":{"finish":"https://streamonpod.com/payment/finish"},"custom_expiry":{"expiry_duration":1440,"unit":"minute"}}',
      params: {},
      auth: [Object]
    },
    request: ClientRequest {
      _events: [Object: null prototype],
      _eventsCount: 7,
      _maxListeners: undefined,
      outputData: [],
      outputSize: 0,
      writable: true,
      destroyed: true,
      _last: false,
      chunkedEncoding: false,
      shouldKeepAlive: true,
      maxRequestsOnConnectionReached: false,
      _defaultKeepAlive: true,
      useChunkedEncodingByDefault: true,
      sendDate: false,
      _removedConnection: false,
      _removedContLen: false,
      _removedTE: false,
      strictContentLength: false,
      _contentLength: 177,
      _hasBody: true,
      _trailer: '',
      finished: true,
      _headerSent: true,
      _closed: true,
      socket: [TLSSocket],
      _header: 'POST /snap/v1/transactions HTTP/1.1\r\n' +
        'Accept: application/json\r\n' +
        'Content-Type: application/json\r\n' +
        'user-agent: midtransclient-nodejs/1.4.2\r\n' +
        'Content-Length: 177\r\n' +
        'Host: app.midtrans.com\r\n' +
        'Authorization: Basic TWlkLXNlcnZlci03UU45cm5KVk5nOUFob1ZWQ3FDSF9fYzQ6\r\n' +
        'Connection: keep-alive\r\n' +
        '\r\n',
      _keepAliveTimeout: 0,
      _onPendingData: [Function: nop],
      agent: [Agent],
      socketPath: undefined,
      method: 'POST',
      maxHeaderSize: undefined,
      insecureHTTPParser: undefined,
      joinDuplicateHeaders: undefined,
      path: '/snap/v1/transactions',
      _ended: true,
      res: [IncomingMessage],
      aborted: false,
      timeoutCb: null,
      upgradeOrConnect: false,
      parser: null,
      maxHeadersCount: null,
      reusedSocket: false,
      host: 'app.midtrans.com',
      protocol: 'https:',
      _redirectable: [Writable],
      [Symbol(kCapture)]: false,
      [Symbol(kBytesWritten)]: 0,
      [Symbol(kNeedDrain)]: false,
      [Symbol(corked)]: 0,
      [Symbol(kOutHeaders)]: [Object: null prototype],
      [Symbol(errored)]: null,
      [Symbol(kHighWaterMark)]: 16384,
      [Symbol(kRejectNonStandardBodyWrites)]: false,
      [Symbol(kUniqueHeaders)]: null
    },
    data: { error_messages: [Array] }
  }
}
2025-07-12T06:40:53.182Z [ERROR] [FFMPEG_STDERR] 4e6badce-9d2d-4c47-a192-eb8b4147b498: rtmp://a.rtmp.youtube.com/live2/t44tg34g4g4g43g34g34g: I/O error
2025-07-12T06:40:53.209Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 4e6badce-9d2d-4c47-a192-eb8b4147b498
2025-07-12T06:40:57.744Z [ERROR] [FFMPEG_STDERR] 4e6badce-9d2d-4c47-a192-eb8b4147b498: rtmp://a.rtmp.youtube.com/live2/t44tg34g4g4g43g34g34g: I/O error
2025-07-12T06:40:57.752Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 4e6badce-9d2d-4c47-a192-eb8b4147b498
2025-07-12T06:41:03.144Z [ERROR] [FFMPEG_STDERR] 4e6badce-9d2d-4c47-a192-eb8b4147b498: rtmp://a.rtmp.youtube.com/live2/t44tg34g4g4g43g34g34g: I/O error
2025-07-12T06:41:03.154Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 4e6badce-9d2d-4c47-a192-eb8b4147b498
2025-07-12T06:41:36.581Z [ERROR] Error fetching current UTC time: connect ETIMEDOUT **************:443
2025-07-12T07:34:24.408Z [ERROR] [FFMPEG_STDERR] 3027f2e5-2f7f-4080-b272-849003443778: rtmp://a.rtmp.youtube.com/live2/f4eg453g45v45g5gfefesfffer: I/O error
2025-07-12T07:34:24.415Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 3027f2e5-2f7f-4080-b272-849003443778
2025-07-12T07:34:28.879Z [ERROR] Error fetching current UTC time: connect ETIMEDOUT **************:443
2025-07-12T07:34:29.932Z [ERROR] [FFMPEG_STDERR] 3027f2e5-2f7f-4080-b272-849003443778: rtmp://a.rtmp.youtube.com/live2/f4eg453g45v45g5gfefesfffer: I/O error
2025-07-12T07:34:29.939Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 3027f2e5-2f7f-4080-b272-849003443778
2025-07-12T07:34:32.941Z [ERROR] 🚨 [ERROR] {
  "errorId": "03a1eb34-d0f6-4842-b2d0-56f16e5a58f3",
  "timestamp": "2025-07-12T07:34:32.940Z",
  "type": "STREAMING_ERROR",
  "message": "Stream is already active",
  "statusCode": 500,
  "stack": "AppError: Stream is already active\n    at createStreamingError (C:\\Users\\<USER>\\OriDrive\\Desktop\\StreamOnPod\\StreamOnPod\\utils\\errorHandler.js:236:10)\n    at startStream (C:\\Users\\<USER>\\OriDrive\\Desktop\\StreamOnPod\\StreamOnPod\\services\\streamingService.js:803:13)\n    at Timeout._onTimeout (C:\\Users\\<USER>\\OriDrive\\Desktop\\StreamOnPod\\StreamOnPod\\services\\streamingService.js:1014:40)",
  "streamId": "3027f2e5-2f7f-4080-b272-849003443778",
  "operation": "startStream",
  "context": {
    "streamId": "3027f2e5-2f7f-4080-b272-849003443778",
    "operation": "start"
  }
}
2025-07-12T07:34:32.942Z [ERROR] [StreamingService] Failed to restart stream: Stream is already active
2025-07-12T07:35:19.444Z [ERROR] [FFMPEG_STDERR] 3027f2e5-2f7f-4080-b272-849003443778: rtmp://a.rtmp.youtube.com/live2/f4eg453g45v45g5gfefesfffer: I/O error
2025-07-12T07:35:19.455Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream 3027f2e5-2f7f-4080-b272-849003443778
2025-07-12T07:46:08.214Z [ERROR] ❌ Error creating stream-optimized indexes: [Error: SQLITE_ERROR: no such table: main.permissions] {
  errno: 1,
  code: 'SQLITE_ERROR'
}
2025-07-12T07:46:08.215Z [ERROR] ❌ Failed to initialize database optimizations: [Error: SQLITE_ERROR: no such table: main.permissions] {
  errno: 1,
  code: 'SQLITE_ERROR'
}
2025-07-12T07:46:32.476Z [ERROR] -----------------------------------
2025-07-12T07:46:32.478Z [ERROR] UNCAUGHT EXCEPTION: TypeError: QuotaMiddleware.checkSubscriptionAndQuota is not a function
    at Object.<anonymous> (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\app.js:3312:59)
    at Module._compile (node:internal/modules/cjs/loader:1376:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)
    at Module.load (node:internal/modules/cjs/loader:1207:32)
    at Module._load (node:internal/modules/cjs/loader:1023:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:135:12)
    at node:internal/main/run_main_module:28:49
2025-07-12T07:46:32.479Z [ERROR] -----------------------------------
2025-07-12T07:46:49.652Z [ERROR] -----------------------------------
2025-07-12T07:46:49.654Z [ERROR] UNCAUGHT EXCEPTION: TypeError: QuotaMiddleware.checkSubscriptionAndQuota is not a function
    at Object.<anonymous> (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\app.js:3312:59)
    at Module._compile (node:internal/modules/cjs/loader:1376:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)
    at Module.load (node:internal/modules/cjs/loader:1207:32)
    at Module._load (node:internal/modules/cjs/loader:1023:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:135:12)
    at node:internal/main/run_main_module:28:49
2025-07-12T07:46:49.655Z [ERROR] -----------------------------------
2025-07-12T07:47:00.538Z [ERROR] -----------------------------------
2025-07-12T07:47:00.540Z [ERROR] UNCAUGHT EXCEPTION: TypeError: QuotaMiddleware.checkSubscriptionAndQuota is not a function
    at Object.<anonymous> (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\app.js:3312:59)
    at Module._compile (node:internal/modules/cjs/loader:1376:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)
    at Module.load (node:internal/modules/cjs/loader:1207:32)
    at Module._load (node:internal/modules/cjs/loader:1023:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:135:12)
    at node:internal/main/run_main_module:28:49
2025-07-12T07:47:00.541Z [ERROR] -----------------------------------
2025-07-12T07:47:14.120Z [ERROR] -----------------------------------
2025-07-12T07:47:14.123Z [ERROR] UNCAUGHT EXCEPTION: TypeError: QuotaMiddleware.checkActiveAccount is not a function
    at Object.<anonymous> (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\app.js:652:19)
    at Module._compile (node:internal/modules/cjs/loader:1376:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)
    at Module.load (node:internal/modules/cjs/loader:1207:32)
    at Module._load (node:internal/modules/cjs/loader:1023:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:135:12)
    at node:internal/main/run_main_module:28:49
2025-07-12T07:47:14.124Z [ERROR] -----------------------------------
2025-07-12T07:49:08.939Z [ERROR] -----------------------------------
2025-07-12T07:49:08.941Z [ERROR] UNCAUGHT EXCEPTION: TypeError: QuotaMiddleware.checkActiveAccount is not a function
    at Object.<anonymous> (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\app.js:652:19)
    at Module._compile (node:internal/modules/cjs/loader:1376:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)
    at Module.load (node:internal/modules/cjs/loader:1207:32)
    at Module._load (node:internal/modules/cjs/loader:1023:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:135:12)
    at node:internal/main/run_main_module:28:49
2025-07-12T07:49:08.942Z [ERROR] -----------------------------------
2025-07-12T07:54:31.590Z [ERROR] ❌ Error creating stream-optimized indexes: [Error: SQLITE_ERROR: no such table: main.permissions] {
  errno: 1,
  code: 'SQLITE_ERROR'
}
2025-07-12T07:54:31.591Z [ERROR] ❌ Failed to initialize database optimizations: [Error: SQLITE_ERROR: no such table: main.permissions] {
  errno: 1,
  code: 'SQLITE_ERROR'
}
2025-07-12T07:55:01.352Z [ERROR] Error creating stream: Error [ERR_HTTP_HEADERS_SENT]: Cannot set headers after they are sent to the client
    at ServerResponse.setHeader (node:_http_outgoing:652:11)
    at ServerResponse.header (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:794:10)
    at ServerResponse.send (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:174:12)
    at ServerResponse.json (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:278:15)
    at C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\app.js:3510:9 {
  code: 'ERR_HTTP_HEADERS_SENT'
}
2025-07-12T07:55:01.353Z [ERROR] -----------------------------------
2025-07-12T07:55:01.354Z [ERROR] UNHANDLED REJECTION AT: Promise {
  <rejected> Error [ERR_HTTP_HEADERS_SENT]: Cannot set headers after they are sent to the client
      at ServerResponse.setHeader (node:_http_outgoing:652:11)
      at ServerResponse.header (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:794:10)
      at ServerResponse.send (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:174:12)
      at ServerResponse.json (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:278:15)
      at C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\app.js:3513:21 {
    code: 'ERR_HTTP_HEADERS_SENT'
  }
}
2025-07-12T07:55:01.355Z [ERROR] REASON: Error [ERR_HTTP_HEADERS_SENT]: Cannot set headers after they are sent to the client
    at ServerResponse.setHeader (node:_http_outgoing:652:11)
    at ServerResponse.header (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:794:10)
    at ServerResponse.send (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:174:12)
    at ServerResponse.json (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:278:15)
    at C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\app.js:3513:21 {
  code: 'ERR_HTTP_HEADERS_SENT'
}
2025-07-12T07:55:01.355Z [ERROR] -----------------------------------
2025-07-12T07:57:14.714Z [ERROR] Error creating stream: Error [ERR_HTTP_HEADERS_SENT]: Cannot set headers after they are sent to the client
    at ServerResponse.setHeader (node:_http_outgoing:652:11)
    at ServerResponse.header (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:794:10)
    at ServerResponse.send (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:174:12)
    at ServerResponse.json (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:278:15)
    at C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\app.js:3328:30
    at Layer.handle [as handle_request] (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\router\layer.js:95:5)
    at next (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\router\route.js:149:13)
    at middleware (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express-validator\lib\middlewares\check.js:16:13) {
  code: 'ERR_HTTP_HEADERS_SENT'
}
2025-07-12T07:57:14.716Z [ERROR] -----------------------------------
2025-07-12T07:57:14.719Z [ERROR] UNHANDLED REJECTION AT: Promise {
  <rejected> Error [ERR_HTTP_HEADERS_SENT]: Cannot set headers after they are sent to the client
      at ServerResponse.setHeader (node:_http_outgoing:652:11)
      at ServerResponse.header (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:794:10)
      at ServerResponse.send (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:174:12)
      at ServerResponse.json (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:278:15)
      at C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\app.js:3513:21
      at Layer.handle [as handle_request] (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\router\layer.js:95:5)
      at next (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\router\route.js:149:13)
      at middleware (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express-validator\lib\middlewares\check.js:16:13) {
    code: 'ERR_HTTP_HEADERS_SENT'
  }
}
2025-07-12T07:57:14.720Z [ERROR] REASON: Error [ERR_HTTP_HEADERS_SENT]: Cannot set headers after they are sent to the client
    at ServerResponse.setHeader (node:_http_outgoing:652:11)
    at ServerResponse.header (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:794:10)
    at ServerResponse.send (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:174:12)
    at ServerResponse.json (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:278:15)
    at C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\app.js:3513:21
    at Layer.handle [as handle_request] (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\router\layer.js:95:5)
    at next (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\router\route.js:149:13)
    at middleware (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express-validator\lib\middlewares\check.js:16:13) {
  code: 'ERR_HTTP_HEADERS_SENT'
}
2025-07-12T07:57:14.721Z [ERROR] -----------------------------------
2025-07-12T07:57:31.489Z [ERROR] Error updating stream status: Error [ERR_HTTP_HEADERS_SENT]: Cannot set headers after they are sent to the client
    at ServerResponse.setHeader (node:_http_outgoing:652:11)
    at ServerResponse.header (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:794:10)
    at ServerResponse.send (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:174:12)
    at ServerResponse.json (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:278:15)
    at C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\app.js:3979:20 {
  code: 'ERR_HTTP_HEADERS_SENT'
}
2025-07-12T07:57:31.489Z [ERROR] -----------------------------------
2025-07-12T07:57:31.490Z [ERROR] UNHANDLED REJECTION AT: Promise {
  <rejected> Error [ERR_HTTP_HEADERS_SENT]: Cannot set headers after they are sent to the client
      at ServerResponse.setHeader (node:_http_outgoing:652:11)
      at ServerResponse.header (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:794:10)
      at ServerResponse.send (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:174:12)
      at ServerResponse.json (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:278:15)
      at C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\app.js:4032:21 {
    code: 'ERR_HTTP_HEADERS_SENT'
  }
}
2025-07-12T07:57:31.490Z [ERROR] REASON: Error [ERR_HTTP_HEADERS_SENT]: Cannot set headers after they are sent to the client
    at ServerResponse.setHeader (node:_http_outgoing:652:11)
    at ServerResponse.header (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:794:10)
    at ServerResponse.send (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:174:12)
    at ServerResponse.json (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:278:15)
    at C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\app.js:4032:21 {
  code: 'ERR_HTTP_HEADERS_SENT'
}
2025-07-12T07:57:31.491Z [ERROR] -----------------------------------
2025-07-12T07:57:31.619Z [ERROR] [FFMPEG_STDERR] dd22457e-05e1-4c63-8d9a-4f527f4240dd: rtmp://a.rtmp.youtube.com/live2/hu90-sbzt-auq3-b6pb-1s7g: I/O error
2025-07-12T07:57:31.627Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream dd22457e-05e1-4c63-8d9a-4f527f4240dd
2025-07-12T07:57:33.754Z [ERROR] Error updating stream status: Error [ERR_HTTP_HEADERS_SENT]: Cannot set headers after they are sent to the client
    at ServerResponse.setHeader (node:_http_outgoing:652:11)
    at ServerResponse.header (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:794:10)
    at ServerResponse.send (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:174:12)
    at ServerResponse.json (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:278:15)
    at C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\app.js:3979:20 {
  code: 'ERR_HTTP_HEADERS_SENT'
}
2025-07-12T07:57:33.755Z [ERROR] -----------------------------------
2025-07-12T07:57:33.755Z [ERROR] UNHANDLED REJECTION AT: Promise {
  <rejected> Error [ERR_HTTP_HEADERS_SENT]: Cannot set headers after they are sent to the client
      at ServerResponse.setHeader (node:_http_outgoing:652:11)
      at ServerResponse.header (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:794:10)
      at ServerResponse.send (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:174:12)
      at ServerResponse.json (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:278:15)
      at C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\app.js:4032:21 {
    code: 'ERR_HTTP_HEADERS_SENT'
  }
}
2025-07-12T07:57:33.756Z [ERROR] REASON: Error [ERR_HTTP_HEADERS_SENT]: Cannot set headers after they are sent to the client
    at ServerResponse.setHeader (node:_http_outgoing:652:11)
    at ServerResponse.header (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:794:10)
    at ServerResponse.send (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:174:12)
    at ServerResponse.json (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:278:15)
    at C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\app.js:4032:21 {
  code: 'ERR_HTTP_HEADERS_SENT'
}
2025-07-12T07:57:33.757Z [ERROR] -----------------------------------
2025-07-12T07:57:34.638Z [ERROR] 🚨 [ERROR] {
  "errorId": "968e1c88-65b0-4587-b865-a408f28a9376",
  "timestamp": "2025-07-12T07:57:34.635Z",
  "type": "STREAMING_ERROR",
  "message": "Stream is already active",
  "statusCode": 500,
  "streamId": "dd22457e-05e1-4c63-8d9a-4f527f4240dd",
  "operation": "startStream",
  "context": {
    "streamId": "dd22457e-05e1-4c63-8d9a-4f527f4240dd",
    "operation": "start"
  }
}
2025-07-12T07:57:34.639Z [ERROR] [StreamingService] Failed to restart stream: Stream is already active
2025-07-12T07:57:36.875Z [ERROR] [FFMPEG_STDERR] dd22457e-05e1-4c63-8d9a-4f527f4240dd: rtmp://a.rtmp.youtube.com/live2/hu90-sbzt-auq3-b6pb-1s7g: I/O error
2025-07-12T07:57:36.882Z [ERROR] [StreamingService] FFmpeg process exited with error code 1 for stream dd22457e-05e1-4c63-8d9a-4f527f4240dd
2025-07-12T07:57:45.655Z [ERROR] Error updating stream status: Error [ERR_HTTP_HEADERS_SENT]: Cannot set headers after they are sent to the client
    at ServerResponse.setHeader (node:_http_outgoing:652:11)
    at ServerResponse.header (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:794:10)
    at ServerResponse.send (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:174:12)
    at ServerResponse.json (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:278:15)
    at C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\app.js:4019:18 {
  code: 'ERR_HTTP_HEADERS_SENT'
}
2025-07-12T07:57:45.657Z [ERROR] -----------------------------------
2025-07-12T07:57:45.658Z [ERROR] UNHANDLED REJECTION AT: Promise {
  <rejected> Error [ERR_HTTP_HEADERS_SENT]: Cannot set headers after they are sent to the client
      at ServerResponse.setHeader (node:_http_outgoing:652:11)
      at ServerResponse.header (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:794:10)
      at ServerResponse.send (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:174:12)
      at ServerResponse.json (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:278:15)
      at C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\app.js:4032:21 {
    code: 'ERR_HTTP_HEADERS_SENT'
  }
}
2025-07-12T07:57:45.659Z [ERROR] REASON: Error [ERR_HTTP_HEADERS_SENT]: Cannot set headers after they are sent to the client
    at ServerResponse.setHeader (node:_http_outgoing:652:11)
    at ServerResponse.header (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:794:10)
    at ServerResponse.send (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:174:12)
    at ServerResponse.json (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:278:15)
    at C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\app.js:4032:21 {
  code: 'ERR_HTTP_HEADERS_SENT'
}
2025-07-12T07:57:45.659Z [ERROR] -----------------------------------
2025-07-12T07:57:55.000Z [ERROR] Error creating stream: Error [ERR_HTTP_HEADERS_SENT]: Cannot set headers after they are sent to the client
    at ServerResponse.setHeader (node:_http_outgoing:652:11)
    at ServerResponse.header (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:794:10)
    at ServerResponse.send (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:174:12)
    at ServerResponse.json (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:278:15)
    at C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\app.js:3510:9 {
  code: 'ERR_HTTP_HEADERS_SENT'
}
2025-07-12T07:57:55.001Z [ERROR] -----------------------------------
2025-07-12T07:57:55.001Z [ERROR] UNHANDLED REJECTION AT: Promise {
  <rejected> Error [ERR_HTTP_HEADERS_SENT]: Cannot set headers after they are sent to the client
      at ServerResponse.setHeader (node:_http_outgoing:652:11)
      at ServerResponse.header (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:794:10)
      at ServerResponse.send (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:174:12)
      at ServerResponse.json (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:278:15)
      at C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\app.js:3513:21 {
    code: 'ERR_HTTP_HEADERS_SENT'
  }
}
2025-07-12T07:57:55.002Z [ERROR] REASON: Error [ERR_HTTP_HEADERS_SENT]: Cannot set headers after they are sent to the client
    at ServerResponse.setHeader (node:_http_outgoing:652:11)
    at ServerResponse.header (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:794:10)
    at ServerResponse.send (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:174:12)
    at ServerResponse.json (C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\node_modules\express\lib\response.js:278:15)
    at C:\Users\<USER>\OriDrive\Desktop\StreamOnPod\StreamOnPod\app.js:3513:21 {
  code: 'ERR_HTTP_HEADERS_SENT'
}
2025-07-12T07:57:55.002Z [ERROR] -----------------------------------
2025-07-12T08:05:06.648Z [ERROR] Error fetching current UTC time: connect ETIMEDOUT **************:443
2025-07-12T08:07:34.666Z [ERROR] -----------------------------------
2025-07-12T08:07:34.669Z [ERROR] UNCAUGHT EXCEPTION: Error: listen EADDRINUSE: address already in use 0.0.0.0:7575
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at doListen (node:net:2069:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '0.0.0.0',
  port: 7575
}
2025-07-12T08:07:34.670Z [ERROR] -----------------------------------
2025-07-12T08:56:34.313Z [ERROR] Error fetching current UTC time: connect ETIMEDOUT **************:443
2025-07-12T10:07:50.732Z [ERROR] Error fetching current UTC time: connect ETIMEDOUT **************:443
2025-07-12T10:10:27.414Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T10:13:44.341Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T10:14:07.312Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T10:15:33.010Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T10:16:29.533Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T10:22:26.313Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T10:28:48.118Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T10:28:48.651Z [ERROR] -----------------------------------
2025-07-12T10:28:48.653Z [ERROR] UNCAUGHT EXCEPTION: Error: listen EADDRINUSE: address already in use 0.0.0.0:7575
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at doListen (node:net:2069:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '0.0.0.0',
  port: 7575
}
2025-07-12T10:28:48.654Z [ERROR] -----------------------------------
2025-07-12T10:29:32.031Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T10:29:34.041Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T10:31:31.891Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T10:52:30.277Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T10:52:32.291Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T10:55:52.285Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:00.429Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:02.437Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:04.460Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:06.478Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:08.500Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:10.515Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:12.532Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:14.540Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:16.559Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:18.575Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:20.595Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:22.606Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:24.628Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:26.646Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:28.663Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:29.994Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:30.675Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:32.005Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:32.689Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:34.023Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:34.706Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:36.029Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:36.726Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:38.051Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:38.733Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:40.064Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:40.746Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:42.074Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:42.764Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:44.091Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:44.788Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:46.103Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:46.809Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:48.117Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:48.835Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:50.134Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:50.846Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:52.143Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:52.859Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:54.158Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:54.865Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:56.173Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:56.878Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:58.196Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:04:58.886Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:05:00.205Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:05:00.905Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:05:02.223Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:05:02.913Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:05:04.237Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:05:04.923Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:05:06.260Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:05:06.940Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:05:08.273Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:05:08.956Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:05:10.288Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:05:10.972Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:05:12.308Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:05:12.989Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:05:15.005Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:05:17.019Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:05:19.036Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:05:21.056Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:05:23.062Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:05:25.073Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:05:27.087Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:06:44.928Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:07:18.856Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:07:20.871Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:07:22.883Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:07:24.903Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:07:26.912Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:07:28.919Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:07:30.941Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:07:32.952Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:07:34.964Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:07:36.983Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:07:39.001Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:07:41.019Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:07:43.039Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:07:45.055Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:07:47.073Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:07:48.483Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:07:49.089Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:07:50.500Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:07:51.100Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:07:52.515Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:07:53.109Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:07:54.533Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:07:55.126Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:07:56.543Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:07:57.136Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:07:58.552Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:07:59.153Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:08:00.563Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:08:01.169Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:08:02.578Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:08:03.180Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:08:04.597Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:08:05.185Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:08:06.616Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:08:07.199Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:08:08.627Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:08:09.214Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:08:10.643Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:08:11.231Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:08:12.658Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:08:13.241Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:08:14.666Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:08:15.265Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:08:16.688Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:08:17.282Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:08:18.695Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:08:19.324Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:08:20.720Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:08:21.339Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:08:22.731Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:08:23.351Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:08:24.743Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:08:25.361Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:08:26.754Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:08:27.373Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:08:28.775Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:08:29.381Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:08:30.786Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:08:31.398Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:08:33.403Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:08:35.411Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:08:37.424Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:08:39.433Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:08:41.443Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:08:43.459Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:08:45.464Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:09:32.941Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:09:34.961Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:09:36.973Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:09:38.979Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:09:40.995Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:09:43.007Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:09:46.741Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:09:48.754Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:09:50.761Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:09:52.779Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:09:54.788Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:09:56.795Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:09:58.806Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:10:00.822Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:10:02.837Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:10:04.847Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:10:06.853Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:10:08.867Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:10:10.886Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:10:12.892Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:10:18.324Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:10:20.333Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:10:22.354Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:10:24.366Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:10:26.387Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:10:30.894Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:10:32.899Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:10:34.909Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:10:36.923Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:10:38.931Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:10:40.938Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:10:42.945Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:10:44.949Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:10:49.080Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:10:51.088Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:10:53.108Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:10:55.118Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:10:57.128Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:10:59.142Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:01.156Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:03.171Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:05.181Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:07.193Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:09.200Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:11.212Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:13.232Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:15.241Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:17.255Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:18.716Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:19.270Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:20.719Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:21.280Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:22.732Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:23.286Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:24.735Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:25.292Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:26.753Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:27.297Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:28.777Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:29.313Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:30.794Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:31.320Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:32.806Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:33.337Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:34.811Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:35.354Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:36.824Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:37.370Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:38.830Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:39.380Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:40.842Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:41.391Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:42.930Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:43.397Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:44.970Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:45.404Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:47.026Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:47.411Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:49.035Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:49.434Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:51.049Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:51.442Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:55.789Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:57.803Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:11:59.809Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:12:01.822Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:12:03.843Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:12:05.862Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:12:10.916Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:12:12.928Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:12:14.945Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:12:16.953Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:12:18.969Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:12:20.984Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:12:22.991Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:12:25.008Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:12:27.020Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:12:29.025Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:12:31.037Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:12:33.044Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:12:35.056Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:12:37.071Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:12:39.089Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:12:40.472Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:12:41.104Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:12:42.489Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:12:43.123Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:12:44.502Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:12:45.141Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:12:49.116Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:12:51.123Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:12:53.137Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:12:55.153Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:12:57.168Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:12:59.188Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:01.205Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:03.213Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:05.232Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:07.248Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:09.256Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:11.268Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:13.282Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:15.286Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:17.293Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:18.748Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:19.307Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:20.752Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:21.314Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:22.767Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:23.317Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:24.783Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:25.336Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:26.792Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:27.347Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:28.796Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:29.350Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:30.809Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:31.367Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:32.821Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:33.406Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:34.843Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:35.410Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:36.859Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:37.423Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:38.864Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:39.432Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:40.871Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:41.446Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:42.883Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:43.452Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:44.885Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:45.463Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:46.898Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:47.476Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:48.905Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:49.492Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:50.921Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:51.511Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:52.932Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:53.523Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:54.954Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:55.539Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:56.961Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:57.553Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:58.977Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:13:59.567Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:14:00.991Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:14:01.574Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:14:03.587Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:14:05.602Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:14:07.615Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:14:09.633Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:14:11.636Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:14:13.663Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:14:15.675Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:17:31.902Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:17:54.373Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:17:56.386Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:17:58.405Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:18:00.420Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:18:02.436Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:18:04.452Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:18:06.466Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:18:08.476Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:18:10.487Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:18:12.504Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:18:17.388Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:18:33.024Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:18:35.039Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:18:37.049Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:18:39.055Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:18:41.065Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:18:43.078Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:18:45.091Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:18:47.102Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:18:49.121Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:18:51.139Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:18:53.207Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:18:55.303Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:18:57.339Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:18:59.347Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:01.369Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:02.619Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:03.378Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:04.624Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:05.383Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:06.645Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:07.398Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:08.654Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:09.414Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:10.668Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:11.422Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:12.681Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:13.573Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:14.691Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:15.577Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:16.695Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:17.629Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:18.707Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:19.639Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:20.725Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:21.661Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:22.744Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:23.671Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:24.759Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:25.693Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:26.805Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:27.709Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:28.820Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:29.732Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:30.853Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:31.756Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:32.868Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:33.776Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:34.880Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:35.781Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:36.889Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:37.789Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:38.909Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:39.792Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:40.918Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:41.803Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:42.926Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:43.820Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:44.942Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:45.837Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:47.852Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:49.873Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:51.882Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:53.890Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:55.899Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:57.911Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:19:59.928Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:21:09.881Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:21:11.894Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:21:13.918Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:21:15.935Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:21:17.948Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:21:19.955Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:21:21.966Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:21:23.983Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:21:25.995Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:21:28.016Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:21:30.025Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:21:32.040Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:21:34.062Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:21:36.078Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:21:38.098Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:21:39.294Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:21:40.104Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:21:41.345Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:21:45.700Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:21:47.709Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:21:49.727Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:21:51.743Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:21:53.762Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:21:55.778Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:21:57.798Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:21:59.809Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:22:01.823Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:22:03.837Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:22:05.857Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:22:07.865Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:22:09.875Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:22:11.887Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:22:13.907Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:22:15.290Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:22:15.924Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:22:17.309Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:22:17.945Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:22:19.329Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:22:19.959Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:22:21.334Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:22:21.969Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:22:23.351Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:22:23.974Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:22:25.360Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:22:25.985Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:22:27.378Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:22:27.999Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:22:29.384Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:22:30.021Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:22:31.408Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:22:32.040Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:22:33.418Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:22:34.055Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:22:35.438Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:22:36.074Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:22:37.455Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:22:38.094Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:22:42.837Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:22:43.359Z [ERROR] -----------------------------------
2025-07-12T11:22:43.361Z [ERROR] UNCAUGHT EXCEPTION: Error: listen EADDRINUSE: address already in use 0.0.0.0:7575
    at Server.setupListenHandle [as _listen2] (node:net:1872:16)
    at listenInCluster (node:net:1920:12)
    at doListen (node:net:2069:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:83:21) {
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '0.0.0.0',
  port: 7575
}
2025-07-12T11:22:43.361Z [ERROR] -----------------------------------
2025-07-12T11:23:05.420Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:25:11.433Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:25:13.452Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:25:15.461Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:25:17.484Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:25:19.500Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:25:21.506Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:25:23.532Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:25:25.555Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:25:27.657Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:25:29.682Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:25:31.769Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:25:33.785Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:25:35.801Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:25:37.808Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:25:39.880Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:25:40.988Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:25:41.890Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:25:42.992Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:25:43.906Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:25:45.007Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:25:45.923Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:25:47.012Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:25:47.931Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:25:49.030Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:25:49.943Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:25:51.036Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:25:51.951Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:25:53.049Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:25:53.963Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:25:55.065Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:25:55.976Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:25:57.079Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:25:57.981Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:25:59.095Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:26:00.000Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:26:01.125Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:26:02.004Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:26:03.136Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:26:04.018Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:26:05.148Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:26:06.028Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:26:07.161Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:26:08.041Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:26:09.182Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:26:10.062Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:26:11.191Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:26:12.079Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:26:13.199Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:26:14.087Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:26:15.204Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:26:16.095Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:26:17.223Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:26:18.104Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:26:19.235Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:26:20.122Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:26:21.252Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:26:22.139Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:26:23.263Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:26:24.145Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:26:29.718Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:26:31.724Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:26:33.743Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:26:35.759Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:26:37.781Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:26:39.800Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:26:41.814Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:26:43.830Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:26:45.839Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:26:47.845Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:26:49.861Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:26:51.872Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:26:53.885Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:26:55.902Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:26:57.946Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:26:59.154Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:26:59.956Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:01.166Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:01.964Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:03.174Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:03.973Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:05.182Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:05.993Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:07.201Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:08.002Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:09.213Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:10.006Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:11.232Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:12.010Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:13.247Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:14.053Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:15.260Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:16.065Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:17.278Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:18.083Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:19.293Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:20.096Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:21.303Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:22.109Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:23.317Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:24.115Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:25.326Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:26.130Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:27.342Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:28.145Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:29.356Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:30.153Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:31.370Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:32.161Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:33.379Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:34.172Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:35.395Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:36.177Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:37.406Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:38.186Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:39.414Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:40.195Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:41.422Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:42.213Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:44.217Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:46.276Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:48.284Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:50.299Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:52.307Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:54.321Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:27:56.332Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:30:24.274Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:30:26.280Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:30:28.296Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:30:30.310Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:30:32.319Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:30:34.326Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:30:36.336Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:30:38.345Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:30:40.356Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:30:42.375Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:30:44.392Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:30:46.400Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:30:48.423Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:30:50.443Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:30:52.465Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:30:53.864Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:30:54.481Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:30:55.881Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:30:56.500Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:30:57.899Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:30:58.508Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:30:59.917Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:00.516Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:01.938Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:02.530Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:03.946Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:04.552Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:05.965Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:06.564Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:07.973Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:08.579Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:09.994Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:10.584Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:11.997Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:12.599Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:14.015Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:14.614Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:16.026Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:16.634Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:18.045Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:18.652Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:20.051Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:20.675Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:22.062Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:22.680Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:24.073Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:24.695Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:26.089Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:26.711Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:28.105Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:28.726Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:30.130Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:30.737Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:32.142Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:32.764Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:34.158Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:34.777Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:36.163Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:36.797Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:38.816Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:40.828Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:42.840Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:44.853Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:46.860Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:48.878Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:31:50.889Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:33:11.208Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:33:13.219Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:33:15.227Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:33:17.231Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:33:19.242Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:33:21.260Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:33:23.275Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:33:25.289Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:33:27.306Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:33:29.320Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:33:34.570Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:33:36.585Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:33:38.623Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:33:40.632Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:33:42.640Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:33:44.653Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:33:46.676Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:33:48.695Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:33:50.704Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:33:52.723Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:33:54.750Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:33:56.760Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:33:58.775Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:00.781Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:02.795Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:04.081Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:04.801Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:06.087Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:06.816Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:08.109Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:08.827Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:10.125Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:10.836Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:12.144Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:12.844Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:14.159Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:14.856Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:16.178Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:16.874Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:18.193Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:18.895Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:20.210Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:20.910Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:22.226Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:22.927Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:24.243Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:24.944Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:26.258Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:26.959Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:28.279Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:28.975Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:30.289Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:30.988Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:32.307Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:32.992Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:34.326Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:35.007Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:36.338Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:37.020Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:38.357Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:39.041Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:40.370Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:41.055Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:42.378Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:43.078Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:44.387Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:45.090Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:46.394Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:47.108Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:49.119Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:51.140Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:53.147Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:55.168Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:57.173Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:34:59.184Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:35:01.188Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:36:36.426Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:36:38.432Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:36:40.446Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:36:42.458Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:36:44.468Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:36:46.486Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:36:48.495Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:37:33.329Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:37:35.343Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:37:37.360Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:37:39.369Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:37:41.390Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:37:43.394Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:37:45.412Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:37:47.421Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:37:49.432Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:37:51.440Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:37:53.457Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:37:55.460Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:37:57.481Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:37:59.493Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:38:01.508Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:38:02.939Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:38:03.518Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:38:04.952Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:38:05.524Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:38:06.960Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:38:07.542Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:38:08.977Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:38:09.552Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:38:10.985Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:38:11.558Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:38:13.001Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:38:13.569Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:38:15.021Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:38:15.583Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:38:17.033Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:38:17.602Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:38:19.052Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:38:19.615Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:38:21.070Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:38:21.632Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:38:23.091Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:38:23.642Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:38:25.107Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:38:25.650Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
2025-07-12T11:38:27.119Z [ERROR] Error fetching current UTC time: timeout of 2000ms exceeded
